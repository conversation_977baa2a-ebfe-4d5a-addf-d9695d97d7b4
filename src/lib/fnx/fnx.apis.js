
var fnxDb = require('./fnx.db');
var ps = require('ps-node');
var kill = require('tree-kill');
var fs = require('fs')
const fss = require('fs').promises;
const shell = require('shelljs');
const { exec } = require('child_process');
const path = require('path');
const cronstrue = require('cronstrue');
const Binance = require('node-binance-api');
const childProcess = require('child_process');
var spawn = require('child_process').spawn;
// var execfile = require('child_process').execFile
const { v4: uuidv4 } = require('uuid');
const modeDebug = true;
const fnxCore = require('./fnx.core');
const rulesetparams = require('../battle.ruleset.params');

const Redis = require('ioredis');
const redixPrefix = require('../redis.prefix.js');
const { default: sqlTradesClient } = require('../sqltradeslite'); 

const fnx = require('../../../nodes/_.functions.js')
const fnxIdx = require('../../../nodes/_.functions.indicators.js');
const fnxDex = require('../../../nodes/_.functions.dex.js');
const battleExchangeInfo = require('../battle.exchangeinfo.json');

const cronParser = require('cron-parser');
const timestamp = exports.timestamp = () => `[${new Date().toUTCString()}]`
const log = exports.log = (...args) => console.log(timestamp(), ...args);
const sleep = exports.sleep = (ms = 300) => { return new Promise(resolve => setTimeout(resolve, ms)); }

const dexSync_init_checkPosTPLs = true;
const commissionRate = 0.0004;
const mongoDbCollections = {
    dbName: 'algoweb',
    dbCollections: {
        users: 'app.users',
        userotps: 'app.users.otp',
        strategies: 'gauss.strategies',
    }
}
const redisKeys = {
    battle_init: 'battle_init',
};

const battle = exports.battle = {
    list: prx => {
        const { redisClient, dbConn, sqlClient, sqlTradesClient, slug, query, body } = prx;
        let refTable = 'battle_init';
        return new Promise(async (resolve, reject) => {
            try {
                let redisKey = refTable;
                const redisVal = await redisClient.get(redisKey);
                let records = redisVal && typeof redisVal !== 'object' ? JSON.parse(redisVal) : redisVal;
                resolve([records])
            } catch (e) {
                reject(e);
            }
        });
    },
    createRecord: ({ redisClient, battleData }) => {
        return new Promise(async (resolve, reject) => {
            try {
                let redisKey = 'battle_init';
                await redisClient.set(redisKey, JSON.stringify(battleData));
                resolve(true);
            }
            catch (e) {
                modeDebug && log('error: battle create record ', e)
                reject('createRecord terminated db cannot updated!')
            }
        });
    },
    start: prx => {
        var { redisClient, dbConn, sqlClient, 
                sqlTradesClient, slug, query, 
                body, 
                dbConnMongo,
                // dexApi 
            } = prx;
        return new Promise(async (resolve, reject) => {
            var dexApi;
            var dtBOPP = Date.now();
            var section = 'start_battle';
            var sTrack = {};
            try {
                let params = {};
                await battle.cleanPrevious({redisClient, purge: true});
                let battleID = fnx.generateKey({ inclTime: true }); // uuidv4();
                try {
                    params = JSON.parse(body)
                } catch (e1) {
                    reject(e1)
                }
                //create Battle Data
                //create Battle Data
                // await battle.initBattleTables({ sqlClient, sqlTradesClient });
                let battleData = {
                    'battleID': battleID,
                    'battle_type': params.type,
                    'battle_params': JSON.stringify(params),
                    'is_active': true,
                    'is_deleted': false,
                    'dtCreated': new Date(Date.now()).toISOString(),
                    'dtUpdated': new Date(Date.now()).toISOString(),
                    // ...params.parameters,
                }
                // log('ignite battle');
                await battle.createRecord({ redisClient, battleData });
                await fnx.logX({
                    redisClient, param: 'GAUSS_GENERIC', value: {
                        section,
                        note: 'battleNode.createRecord: ignite battle',
                        symbol: 'GAUSS_GENERIC',
                        resp: {battleID, battleData},
                    }
                });

                //fnCreateNodeData;
                const { pairs, battleInterval, intervals, limit, dataRefreshIntervals, indicatorsWParams, trading, battleType } = params?.parameters;
                //DONE: !!SECILEN PAIR LER ILE AKTIF POZLARI OLAN PAIR LERIN MUTABAKATI!
                // let isBattleInDex = battleType && battleType.dex == 'exchange'
                let isBattleInDex = await fnxDex.dex.isBattleInExchange({redisClient});
                console.log('isBattleInDex', isBattleInDex)
                if (isBattleInDex) {
                    dexApi = await fnxDex.dex.getDexApi({ redisClient, battle_params: params?.parameters });
                    //sync w/dex
                    if (dbConnMongo) {
                        let battleDatax = typeof battleData !== 'object' ? JSON.parse(battleData) : battleData;
                        let params = battleDatax && typeof battleDatax?.battle_params !== 'object' ? JSON.parse(battleDatax?.battle_params) : battleDatax?.battle_params;
                        delete params?.parameters?.battleType?.config?.apiSecret
                        battleDatax.battle_params = params;
                        sTrack.battleData = battleDatax;

                        try {
                            const db = dbConnMongo.db(mongoDbCollections.dbName)
                            const coll = db.collection('gauss.battleDexLogs');
                            sTrack.rsgID = fnx.generateKey({ inclTime: false });
                            sTrack.dtupdated = new Date(Date.now()).toISOString();

                            let dexAccount = dexApi && await dex.faccount2({ dexApi, listPositions: true });
                            // sTrack.dexAccount = dexAccount;
                            sTrack = {
                                ...sTrack,
                                ...dexAccount,
                            }
                            try {
                                await coll.insertOne(sTrack);
                            } catch (err) {
                                fnx.log('err', err);
                            }
                        } catch (eX) {
                            fnx.log('err', eX);
                        }
                    } else {
                        fnx.log('no Mongo')
                    }
                }

                // log('params?.parameters', params?.parameters);
                var x = null;
                let currSec = new Date().getSeconds();
                let pairNums = Array.isArray(pairs) ? pairs.length : 0;
                var maxStartSec = pairNums > 100 ? 5 : pairNums > 50 ? 20 : 58;
                var time2Wait = parseInt(currSec > maxStartSec ? 60 - currSec : 0) * 1000;
                time2Wait !== 0 && log('waiting to start', time2Wait);
                await sleep(time2Wait);
                // log('start battle!', 'dex?', isBattleInDex);
                await fnx.logX({
                    redisClient, param: 'GAUSS_GENERIC', value: {
                        section,
                        note: 'start_battle: isBattleInDex:' + JSON.stringify(isBattleInDex) ,
                        symbol: 'GAUSS_GENERIC',
                    }
                });

                let klineNodeParams = battleNode.createKlineData({
                    battleID, pairs, intervals, battleInterval, limit,
                    indicatorsWParams,
                    dataRefreshIntervals
                });

                // log('klineNodeParams set!');
                await fnx.logX({
                    redisClient, param: 'GAUSS_GENERIC', value: {
                        section,
                        note: 'battleNode.createKlineData: klineNodeParams set.',
                        symbol: 'GAUSS_GENERIC',
                        resp: klineNodeParams,
                    }
                });

                await battleNode.createRecord({ redisClient, nodeParams: klineNodeParams })
                // log('klineNode records created!');
                await fnx.logX({
                    redisClient, param: 'GAUSS_GENERIC', value: {
                        section,
                        note: 'battleNode.createRecord: klineNode records created.',
                        symbol: 'GAUSS_GENERIC',
                    }
                });

                let act = await battleNode.startNodeFN({ redisClient });
                // log('nodes started!', act);
                await fnx.logX({
                    redisClient, param: 'GAUSS_GENERIC', value: {
                        section,
                        note: 'battleNode.startNodeFN: nodes started!.',
                        symbol: 'GAUSS_GENERIC',
                        resp: act,
                    }
                });
                await battleNode.startNodeMarketFN({ redisClient, battleID });

                await fnx.logX({
                    redisClient, param: 'GAUSS_GENERIC', value: {
                        section,
                        note: 'battleNode.startNodeMarketFN',
                        symbol: 'GAUSS_GENERIC',
                    }
                });

                if(dexApi) {
                    try {
                        let dtBop = Date.now();
                        await fnx.logX({
                            redisClient, param: 'GAUSS_GENERIC', value: {
                                section,
                                note: 'start battle: DEX ACTIONS: syncDexWBattle -start!',
                                symbol: 'GAUSS_GENERIC',
                            }
                        });
                        await fnxDex.dex.setDexLeverages({ redisClient });
                        await fnxDex.dex.syncDexWBattle({ dexApi, redisClient, section, }); 
                        await fnx.logX({
                            redisClient, param: 'GAUSS_GENERIC', value: {
                                section,
                                note: 'start battle: DEX ACTIONS DONE: syncDexWBattle ' + (Date.now() - dtBop).toString() + 'ms',
                                symbol: 'GAUSS_GENERIC',
                            }
                        });
                    } catch (e) {
                        log('sync dex failed', e)
                    }
                } else {
                    log('no dex api!', dexApi)
                }

                await fnx.logX({
                    redisClient, param: 'GAUSS_GENERIC', value: {
                        section: 'start_battle',
                        note: 'battle started! ' + (Date.now() - dtBOPP).toString() + 'ms',
                        symbol: 'GAUSS_GENERIC',
                    }
                });
                await redisClient.set('appVars:battlestarted', 1);

                resolve({ battleData, klineNodeParams })

            } catch (e) {
                log('error: battleStart ')
                reject(e)
            }
        });
    },
    stop: prx => {
        const { redisClient, dbConn, sqlClient, sqlMarketClient, sqlTradesClient, slug, query, body } = prx;
        return new Promise(async (resolve, reject) => {
            try {
                let params = {};
                try {
                    params = JSON.parse(body)
                } catch (e1) {
                    reject(e1)
                }
                global.dexApi = false;
                // await battle.initBattleTables({ sqlClient, sqlMarketClient, sqlTradesClient })
                await battle.cleanPrevious({ redisClient });
                if (params.purge) {
                    await battle.cleanPrevious({ redisClient, purge: true });
                    fs.writeFile(process.cwd() + '/out.log', '', 'utf8', function (err) {
                        if (err) return console.log(err);
                    });

                    fs.writeFile(process.cwd() + '/nodes/bin/binance.futuresDaily_log.json', '', 'utf8', function (err) {
                        if (err) return console.log(err);
                    });


                    //DONE: purge
                    // sqlMarketClient.;
                    // await market.cleanMarketData({
                    //     sqlClient: sqlMarketClient,
                    //     sqlTradesClient: sqlTradesClient,
                    // })
                    // try {

                    //     let tqq = ` drop table if exists t_orders; `;
                    //     await sqlTradesClient.prepare(tqq).run();

                    //     tqq = ` drop table if exists t_trades; `;
                    //     await sqlTradesClient.prepare(tqq).run();

                    //     let atqq = ` drop table if exists t_orders; `;
                    //     await sqlTradesClient.prepare(atqq).run();

                    //     let qq = ` drop table if exists battle_node_tasks; `;
                    //     await sqlClient.prepare(qq).run();

                    //     let qq1 = ` drop table if exists battle_nodes; `;
                    //     await sqlClient.prepare(qq1).run();

                    //     let qq2 = ` drop table if exists battle_init; `;
                    //     await sqlClient.prepare(qq2).run();

                    //     let qq3 = ` drop table if exists battle_node_task_logs;`;
                    //     await sqlClient.prepare(qq3).run();
                    //     // log('process.cwd()', process.cwd())


                    //     resolve({ result: { qq, qq1, qq2, qq3 } })
                    // }
                    // catch (e1) {
                    //     console.log('node update error', e1)
                    //     reject(e1)
                    // }
                }
                //create Battle Data
                //create Battle Data

                resolve({ params })
            } catch (e) {
                log('error: battle stop ')
                reject(e)
            }
        });
    },
    hardReset: ({
        redisClient,
        dbConn,
        sqlClient,
        sqlMarketClient,
        sqlTradesClient,
        slug,
        query,
        body,
        mainProcessID,
    }) => {

        return new Promise(async (resolve, reject) => {
            let arr = []
            try {
                //killall node
                global.dexApi = false;
                let portList = await battleNode.activeNodePorts();
                console.log('nodes terminated. get list: lsof -c node -a -i', portList, mainProcessID);

                try {
                    await battle.cleanPrevious({redisClient, purge: false});
                } catch (e13) {}
                if (query && query.f && query.f !== '') {
                    //DONE: clean market data;
                    try {
                        await battle.cleanPrevious({ redisClient, purge: true });
                    } catch (e13) { }
                    fs.writeFile(process.cwd() + '/out.log', '', 'utf8', function (err) {
                        if (err) return console.log(err);
                    });

                    exec('killall node', (err, stdout, stderr) => { //ps aux | grep node
                        if (err) {
                            console.log(`err: ${err}`);
                            return;
                        }
                        log(`stdout: ${stdout}`);
                        resolve(stdout)
                    });
                }
                Array.isArray(portList) && portList.forEach(function (value) { //.slice(1)
                    parseInt(value) !== parseInt(mainProcessID) && log('terminating node', value);
                    parseInt(value) !== parseInt(mainProcessID) && value !== '' && kill(value, 'SIGKILL', function (err) {
                        if (err) {
                            log('killing err', err);
                            // reject('error in terminating')
                            exec('killall node', (err, stdout, stderr) => { //ps aux | grep node
                                if (err) {
                                    log(`err: ${err}`);
                                    return;
                                }
                                log(`stdout: ${stdout}`);
                                resolve(stdout)
                            });
                        }
                    });
                });
                resolve(true);

            } catch (e) {
                reject(e)
            }
        });
    },
    cleanPrevious: ({ redisClient, terminateNodes = false, purge = false }) => {
        return new Promise(async (resolve, reject) => {
            try {
                await redisClient.del('battle_init');
                await redisClient.del('battle_nodes');
                await redisClient.del('battle_node_tasks'); 
                await redisClient.del('appVars:battlestarted');
                // await redisClient.del('monitor_fetch');
                if (purge) {
                    let keys = await redisClient.keys(redixPrefix.node + '*');
                    Array.isArray(keys) && keys.map(async (k) => {
                        redisClient.del(k);
                    });

                    let keysKline = await redisClient.keys(redixPrefix.data + '*');
                    Array.isArray(keysKline) && keysKline.map(async (k) => {
                        redisClient.del(k);
                    });

                    let keystats = await redisClient.keys(redixPrefix.monitor + '*');
                    Array.isArray(keystats) && keystats.map(async (k) => {
                        redisClient.del(k);
                    });

                    let okeys = await redisClient.keys(redixPrefix.dataOrders + '*');
                    Array.isArray(okeys) && okeys.map(async (k) => {
                        redisClient.del(k);
                    });

                    let tkeys = await redisClient.keys(redixPrefix.dataTrades + '*');
                    Array.isArray(tkeys) && tkeys.map(async (k) => {
                        redisClient.del(k);
                    });

                    let lkeys = await redisClient.keys(redixPrefix.dex.data + '*');
                    Array.isArray(lkeys) && lkeys.map(async (k) => {
                        redisClient.del(k);
                    });

                    var addon = 'nodes/';
                    var folderName = addon + 'bin';
                    var fileinitial = 'backtest';
                    try {
                        var files = await fnx.getlistoffiles(folderName);
                        await fnx.cleanfiles(folderName, files, fileinitial);
                        redisClient.del('backtestData');
                    } catch (e) {
                        console.log('error in backtest simulator files: ', e);
                    };

                    // var folderName = '/nodes/bin/dexDataLogs/';
                    // var fileinitial = 'backtest';
                    // try {
                    //     var files = await fnx.getlistoffiles(folderName);
                    //     await fnx.cleanfiles(folderName, files, 'E_');
                    // } catch (e) {
                    //     console.log('error in dexDataLogs files: ', e);
                    // };

                    log('purge DB', keys && keys.length, keysKline && keysKline.length);
                }

                terminateNodes && log('terminate nodes!')
                //DONE: clean node sessions!
                resolve(true);
            }
            catch (e) {
                log('error: fnDB_cleanPreviousBattles ', e)
                reject('fnDB_cleanPreviousBattles terminated db cannot updated!')
            }
        });

    },
    sqlite_cleanPrevious: ({ sqlClient }) => {
        let refTable = 'battle_init';
        return new Promise(async (resolve, reject) => {
            try {
                let qq = `  
                        UPDATE ${refTable}
                        SET is_active = false, 
                            is_deleted = true, 
                            dtupdated = CURRENT_TIMESTAMP
                        where 1=1;
                    `;
                await sqlClient.prepare(qq).run();
                await battleNode.killAll({ db: sqlClient });
                // modeDebug && log('fnDB_killAllBattleNodes done!')
                let qq21 = `  
                        UPDATE 'battle_node_tasks'
                        SET is_active = false, 
                            is_deleted = true, 
                            dtupdated = CURRENT_TIMESTAMP
                        where 1=1;
                    `;
                await sqlClient.prepare(qq21).run();
                resolve(true);
            }
            catch (e) {
                log('error: fnDB_cleanPreviousBattles ', e)
                reject('fnDB_cleanPreviousBattles terminated db cannot updated!')
            }
        });
        //DONE: clean battle nodes
        //DONE: clean battle node tasks.

    },
    saveparameters: prx => {
        const { dbConn, sqlClient, sqlTradesClient, slug, query, body } = prx;
        return new Promise(async (resolve, reject) => {
            // fnxCore.savetoFile
                let params = {};
                let saveID = fnx.generateKey({ inclTime: true }); // uuidv4();
                try {
                    params = JSON.parse(body)
                } catch (e1) {
                    reject(e1)
                }


            let resp = {}
            resp.fileID = saveID;
            resp.dt = new Date(Date.now()).toISOString();
            resp.data = params;
            try {
                await fnxCore.main.save2file(resp, 'params.' + saveID + '.json', true);
            } catch (e) {
                console.log('error save', e)
            }
            resolve({ data: resp })
        });
    },
    listsavedparameters: prx => {
        const { dbConn, sqlClient, sqlTradesClient, slug, query, body } = prx;
        return new Promise(async (resolve, reject) => {
            let pagePath = path.join(process.cwd(), "public/assets/");
            let files = fs.readdirSync(pagePath);
            resolve(files)
        });
    },
    loadparameters: prx => {
        const { dbConn, sqlClient, sqlTradesClient, slug, query, body } = prx;
        return new Promise(async (resolve, reject) => {
            if (query.file) {
                try {

                let file = query.file;
                let pagePath = path.join(process.cwd(), "public/assets/" + file);
                console.log('loadparameters file pt', pagePath);
                let filesStg = fs.readFileSync(pagePath);
                try {
                    let files = JSON.parse(filesStg);
                    resolve({filedata: files});
                }
                catch (e) {
                    console.log('filesStg', filesStg);
                    reject('json parse error')
                }
                } catch (eF) {
                    console.log('loadparameters', eF);
                    reject('file error')
                }
            } else {
                reject('no file')
            }
        });
    },
    deleteparametersfile: prx => {
        const { dbConn, sqlClient, sqlTradesClient, slug, query, body } = prx;
        return new Promise(async (resolve, reject) => {
            if (query.file) {
                try {

                let file = query.file;
                let pagePath = path.join(process.cwd(), "public/assets/" + file);
                console.log('deleteparametersfile pt', pagePath);
                try {
                    let filesStg = fs.unlinkSync(pagePath);
                    resolve({filedata: filesStg});
                }
                catch (e) {
                    console.log('filesStg', filesStg);
                    reject('json parse error')
                }
                } catch (eF) {
                    console.log('loadparameters', eF);
                    reject('file error')
                }
            } else {
                reject('no file')
            }
        });
    },
    getlogs: props => {
        var { redisClient, slug, query, body } = props;
        return new Promise(async (resolve, reject) => {
            redisClient = redisClient || new Redis({
                host: '127.0.0.1',
                port: 6379,
            });
            let symbol = Array.isArray(slug) && slug[1];
            symbol = symbol ? symbol.toUpperCase() : false;
            let loggs = [];
            let logz = await redisClient.keys(redixPrefix.dex.dataLogs + 'symbols:*');
            await Promise.all(
                logz.map(async (logkey) => {
                    const value = await redisClient.get(logkey);
                    let nodeData = JSON.parse(value);
                    loggs = nodeData && nodeData.length !== 0 ? [...loggs, ...nodeData] : loggs;
                })
            );
            loggs = symbol ? loggs.filter(t => t.value?.symbol == symbol.toUpperCase()) : loggs;
            
            loggs = loggs.sort((a, b) => (a.lTime < b.lTime ? 1 : -1))

            // fnx.log('nodes', loggs)
            resolve({data: loggs})
        });
    },
    getdexlogs: props => {
        var { redisClient, slug, query, body } = props;
        return new Promise(async (resolve, reject) => {
            redisClient = redisClient || new Redis({
                host: '127.0.0.1',
                port: 6379,
            });
            let symbol = Array.isArray(slug) && slug[1];
            symbol = symbol ? symbol.toUpperCase() : false;
            let loggs = [];
            let logz = await redisClient.keys(redixPrefix.dex.dataLogs + 'fnBinance:*');
            await Promise.all(
                logz.map(async (logkey) => {
                    const value = await redisClient.get(logkey);
                    let nodeData = JSON.parse(value);
                    loggs = nodeData && nodeData.length !== 0 ? [...loggs, ...nodeData] : loggs;
                })
            );
            loggs = symbol ? loggs.filter(t => t.value?.symbol == symbol.toUpperCase()) : loggs;
            
            loggs = loggs.sort((a, b) => (a.lTime < b.lTime ? 1 : -1))

            // fnx.log('nodes', loggs)
            resolve({data: loggs})
        });
    },
    isBattleInExchange: props => {
        var { redisClient, slug, query, body } = props;
        return new Promise(async (resolve, reject) => {
            let isBattleInDex = await fnxDex.dex.isBattleInExchange({redisClient});
            resolve(isBattleInDex)
        });
    }
};

const battleNode = exports.battleNode = {
    list: async props => {
        const { redisClient, sqlClient, query } = props;
        const dt = Date.now();
        return new Promise(async (resolve, reject) => {
            try {
                let resp = [];
                let nodes = await redisClient.keys(redixPrefix.node +'*');
                if (Array.isArray(nodes)) {
                    for (n of nodes) {
                        const value = await redisClient.get(n);
                        let nodeData = JSON.parse(value);
                        resp.push(nodeData.nodeSettings);
                    }
                }
                let sure = Date.now() - dt
                resolve({ data: resp, sure });
            }
            catch (e) {
                console.log('nodelist error', e)
                reject({ data: false });
            }
        });
    },

    calcNextWorkTime: ({cronPeriod, promisee = false}) => {
        if (promisee) {
            return new Promise(async (resolve, reject) => {
                let resp = {};
                try {
                    var nodeInterval = cronParser.parseExpression(cronPeriod);
                    let nedeNextWorkTimeStg = nodeInterval.next();
                    let nedeNextWorkTime = new Date(nedeNextWorkTimeStg.toString());
                    let nedeNextInSec = (nedeNextWorkTime - new Date(Date.now())) / 1000;
                    resp.dt = nedeNextWorkTime;
                    resp.sec = parseInt(nedeNextInSec);
                    resolve(resp)
                } catch (e) {
                    reject(e)
                }
            })
        } else {
            let resp = {};
            try {
                var nodeInterval = cronParser.parseExpression(cronPeriod);
                let nedeNextWorkTimeStg = nodeInterval.next();
                let nedeNextWorkTime = new Date(nedeNextWorkTimeStg.toString());
                let nedeNextInSec = (nedeNextWorkTime - new Date(Date.now())) / 1000;
                resp.dt = nedeNextWorkTime;
                resp.sec = parseInt(nedeNextInSec);
                return resp
            } catch (e) {
                return e
            }
        }
    },

    restart: prx => {
        const { redisClient, dbConn, sqlClient, slug, query, body = {}, modeDebug } = prx;
        return new Promise(async (resolve, reject) => {
            var { redisKey, node_key_tag } = JSON.parse(body);
            // log('JSON.parse(body)', JSON.parse(body))
            if (redisKey) {
                try {
                    redisKey && await battleNode.startNode({
                        redisClient,
                        redisKey: redisKey
                    })
                    resolve(true);
                } catch (e) {
                    log('error restart', redisKey, e)
                    reject(false)
                }
            } else if (node_key_tag) {
                let nodeKeystg = await redisClient.keys(redixPrefix.node + '*');
                let nodeKeys = nodeKeystg.filter(x => x.includes(node_key_tag.toLowerCase()));
                log('nodes to restart', nodeKeys);
                for (n of nodeKeys) {
                    try {
                         await battleNode.startNode({
                            redisClient,
                            redisKey: n
                        })
                    } catch (e) {
                        log('error restart', n, e)
                    }
                }
                resolve(true)
            } else {
                reject('what?')
            }
        });
    },
    reconnode: async props => {
        return new Promise(async (resolve, reject) => {
            const { redisClient, slug, token } = props;
            let redisKeya = Array.isArray(slug) && slug[1];

            let nodeKeystg = await redisClient.keys(redixPrefix.node + '*');
            let nodeKeys = nodeKeystg.filter(x => x.includes(redisKeya.toLowerCase()));
            log('nodes to reconn', nodeKeys);
            for (n of nodeKeys) {
                try {
                    let port = false;
                    let redisVal = false;
                    try {
                        redisVal = await redisClient.get(n);
                        if (redisVal) {
                            let redisValStg = JSON.parse(redisVal);
                            let nodeSettings = redisValStg?.nodeSettings;
                            port = nodeSettings.port;
                            fnx.log('reconn', n), port;
                        } else {
                            fnx.log('reconn erro - no redis rec.')
                        }
                    } catch (eR) {
                        fnx.log('reconn erro', eR)
                    }
                    !port && fnx.log('reconn redisVal', redisVal)
                    if (port) {
                        let uri = `http://localhost:${port}/socketreconn`;
                        let res = await fetch(uri, {
                            // mode: 'no-cors',
                            method: 'GET',
                            headers: {
                                'Authorization': 'Bearer ' + token,
                                'X-Host': 'Subanet.com',
                            },
                        });
                        const datax = await res.json();
                    } else {
                        fnx.log('no port data ?' + n, redisVal)
                    }
                } catch (e) {
                    fnx.log('error reconnode', n, e)
                }
            }
            resolve({ data: true })
        });
    },
    sqlite_reconnode: async props => {
        return new Promise(async (resolve, reject) => {
            const { sqlClient, slug, token } = props;
            let node_key_tag = Array.isArray(slug) && slug[1];
            let urix = ''
            try {
                let qq = `  
                    SELECT *
                    FROM battle_nodes
                    Where is_deleted = 0 and node_key_tag ='${node_key_tag}';
                    `;
                let data = sqlClient.prepare(qq).all();
                if (Array.isArray(data) && data.length !== 0) {
                    let node_tasksStg = JSON.parse(data[0]?.node_tasks);
                    let node_tasks = Array.isArray(node_tasksStg?.tasks) && node_tasksStg?.tasks.filter(n => n.taskDesc === 'klineLoader' && n.wsconn == true)
                    if (node_tasks[0] && node_tasks[0]?.taskDesc === 'klineLoader' && node_tasks[0]?.wsconn == true) {
                        let port = data[0]['port'];
                        let uri = `http://localhost:${port}/socketreconn`;
                        urix = uri;
                        let res = await fetch(uri, {
                            // mode: 'no-cors',
                            method: 'GET',
                            headers: {
                                'Authorization': 'Bearer ' + token,
                                'X-Host': 'Subanet.com',
                            },
                        });
                        const datax = await res.json();
                        resolve({ data: true })
                    } else {
                        console.log('reconnode1 ws conn disabled', data);
                        resolve({ data: false })
                    }
                } else {
                    console.log('reconnode2 node not found', data);
                    resolve({ data: false })
                }
            }
            catch (e) {
                log('reconnode error', urix, e)
                reject({ data: false });
            }
        });
    },
    startNode: ({ redisKey, custom_file = false, asyncc = true }) => {
        if (asyncc) {
            return new Promise(async (resolve, reject) => {
                try {
                    var out = fs.openSync('./out.log', 'a'),
                        err = fs.openSync('./out.log', 'a');
                    if (custom_file) {
                        spawn('node', [path.join(process.cwd(), custom_file,), '-redisKey=' + redisKey], {
                            stdio: ['ignore', out, err], // piping stdout and stderr to out.log
                            detached: true
                        }).unref();
                        resolve(true);

                    }
                    else {
                        spawn('node', [path.join(process.cwd(), '/nodes/battleNode.js'), '-redisKey=' + redisKey], {
                            stdio: ['ignore', out, err], // piping stdout and stderr to out.log
                            detached: true
                        }).unref();
                        // log('startNode battleNode.js', node_key);
                        resolve(true);
                    }

                } catch (e) {
                    console.log('error: battleStartNode', e)
                    reject('error: battleStartNode!')
                }
            });
        } else {
            try {

                var out = fs.openSync('./out.log', 'a'),
                    err = fs.openSync('./out.log', 'a');
                if (custom_file) {
                    spawn('node', [path.join(process.cwd(), custom_file,), '-redisKey=' + redisKey], {
                        stdio: ['ignore', out, err], // piping stdout and stderr to out.log
                        detached: true
                    }).unref();
                    return

                }
                else {
                    spawn('node', [path.join(process.cwd(), '/nodes/battleNode.js'), '-redisKey=' + redisKey], {
                        stdio: ['ignore', out, err], // piping stdout and stderr to out.log
                        detached: true
                    }).unref();
                    // log('startNode battleNode.js', node_key);
                    return
                }
            } catch (e) {

            }

        }
    },
    startNodeFN: async ({ redisClient }) => {
        let db = redisClient;
        return new Promise(async (resolve, reject) => {
            try {
                let data = await redisClient.keys(redixPrefix.node + '*');
                if (Array.isArray(data)) {

                    let totall = data.length;
                    let i = 0;
                    for (p of data) {
                        i++;
                        log( i + '/' + totall, 'run node', p);
                        await battleNode.startNode({
                            redisKey: p
                        })
                    }
                    resolve(true)
                } else {
                    reject('no array!')
                }
                // resolve(true)
            }
            catch (e) {
                console.log('error: fnStartBattleNodes', e)
            }
        });
    },
    startNodeMarketFN: async ({ redisClient, battleID }) => {
        return new Promise(async (resolve, reject) => {
            let nnodes = battleNode.getMarketTickerDataScheme({ battleID, inte: '1m' })
            await battleNode.createRecordFN({
                redisClient,
                ...nnodes, refTable: 'battle_nodes'
            });
            await battleNode.startNode({
                redisKey: redixPrefix.node + nnodes.keyTag
            })

            // await battleNode.createTasks({
            //     sqlClient,
            //     ...nnodes, refTable: 'battle_node_tasks'
            // });

            // log('startNodeMarketFN...', nnodes); //, JSON.stringify(nnodes)
            //create market node params.
            resolve(true);
        });
    },
    startNodeMarketTraderFN: async ({ sqlClient, battleID }) => {
        return new Promise(async (resolve, reject) => {
            // -- !! bu ayrı logic battleNode gibi. one time periodic calismiyor.
            await battleNode.startNode({
                node_key: 'Market_Trader',
                custom_file: '/nodes/battleTrader.js',
            });
            log('start market trader');
            resolve(true);
        });
    },
    getMarketTickerDataScheme: props => {
        const { battleID, inte } = props;
        let nodeKey = fnx.generateKey({ inclTime: false }) // Date.now().toString() +  '-' + (+new Date * Math.random()).toString(36).substring(0,6);//Date.now().toString();//uuidv4();
        let nodeParams = {
            'battleID': battleID,
            'type': 'ticker',
            'key': nodeKey,
            'keyTag': 'market_ticker',
            'process_id': 0,
            'port': 0,
            'is_active': true,
            'is_deleted': false,
            'props': {
                'tasks': [
                    {//fetch candleStick Kline!!!!
                        "taskDesc": "marketTicker",
                        "battleID": battleID,
                        "nodeKey": nodeKey,
                        'taskID': nodeKey + '-1',
                        "taskTag": 'mt_futuresDaily',
                        "script2Run": "node.marketTicker.js",
                        "script2RunUri": "node.marketTicker.js " + nodeKey + '-1',
                        "wsconn": true,
                        "wsconnuri": '!ticker@arr',
                        "period": fn.cronTagCreator(inte, 20),
                        "periodDesc": cronstrue.toString(fn.cronTagCreator(inte, 20)),
                        "taskParams": JSON.stringify({
                            battleID,
                            "nodeID": nodeKey,
                            "keyTag": 'mt_futuresDaily',
                            "symbol": 'ALL',
                            "interval": inte,
                        })
                    },
                    {
                        "taskDesc": "marketTicker",
                        "battleID": battleID,
                        "nodeKey": nodeKey,
                        'taskID': nodeKey + '-2',
                        "taskTag": 'mt_fundingRate',
                        "script2Run": "node.marketMore.js",
                        "script2RunUri": "node.marketMore.js " + nodeKey + '-2',
                        "wsconn": false,
                        "wsconnuri": 'ALL' + '@ticker' + inte,
                        "period": fn.cronTagCreator(inte, 20),
                        "periodDesc": cronstrue.toString(fn.cronTagCreator(inte, 20)),
                        "taskParams": JSON.stringify({
                            battleID,
                            "nodeID": nodeKey,
                            "keyTag": 'mt_fundingRate',
                            "symbol": 'ALL',
                            "interval": inte,
                        })
                    },
                    {//fetch candleStick Kline!!!!
                        "taskDesc": "marketTicker",
                        "battleID": battleID,
                        "nodeKey": nodeKey,
                        'taskID': nodeKey + '-3',
                        "taskTag": 'mt_markPrice',
                        "script2Run": "node.marketMore.js",
                        "script2RunUri": "node.marketMore.js " + nodeKey + '-3',
                        "wsconn": false,
                        "wsconnuri": 'ALL' + '@ticker' + inte,
                        "period": fn.cronTagCreator(inte, 20),
                        "periodDesc": cronstrue.toString(fn.cronTagCreator(inte, 20)),
                        "taskParams": JSON.stringify({
                            battleID,
                            "nodeID": nodeKey,
                            "keyTag": 'mt_markPrice',
                            "symbol": 'ALL',
                            "interval": inte,
                        })
                    },//fetch candleStick Kline!!!!
                    //post acts!
                ]
            }
        };
        return nodeParams;
    },
    checkProcessID: ID => {
        return new Promise(async (resolve, reject) => {
            ps.lookup({ pid: ID }, function (err, resultList) {
                if (err) {
                    // throw new Error(err);
                    reject(err)
                }
                var process = resultList[0];
                if (process) {
                    // console.log('PID: %s, COMMAND: %s, ARGUMENTS: %s', process.pid, process.command, process.arguments);
                    resolve(true)
                }
                else {
                    resolve(false)
                }
            });
        });
    },
    checkProcess: (arr, db) => {

        return new Promise(async (resolve, reject) => {
            if (Array.isArray(arr)) {
                for (p of arr) {
                    let isActive = await battleNode.checkProcessID(p.process_id);
                    if (!isActive) {
                        try {
                            let qq = `  
                            UPDATE core_nodes
                            SET is_active = false
                            where process_id = ` + p.process_id;
                            var res = db.prepare(qq).run();
                        }
                        catch (e) {
                            console.log('node update error', e)
                        }
                    }
                }
                resolve(true)
            } else {
                resolve(true);
            }
        });
    },
    killProcess: (process_id, db) => {

        return new Promise(async (resolve, reject) => {
            let isActive = await battleNode.checkProcessID(process_id);
            if (isActive) {
                kill(process_id, 'SIGKILL', function (err) {
                    // Do things
                    console.log(err);
                    reject('error in terminating')
                });
                try {
                    let qq = `  
                    UPDATE core_nodes
                    SET is_active = false
                    where process_id = ` + process_id;
                    db.prepare(qq).run();
                    resolve(true);
                }
                catch (e) {
                    console.log('node update error', e)
                    reject('terminated but db cannot updated!')
                }
            }
            else {
                let qq = `  
                    UPDATE core_nodes
                    SET is_active = false
                    where process_id = ` + process_id;
                db.prepare(qq).run();
                reject('not active process')
            }
        });
    },
    kill: prx => {
        const { redisClient, dbConn, sqlClient, slug, query, body = {}, modeDebug } = prx;
        return new Promise(async (resolve, reject) => {
            var { redisKey } = JSON.parse(body);

            const redisVal = await redisClient.get(redisKey);
            if (redisVal) {
                let redisValStg = JSON.parse(redisVal);
                let nodeSettings = redisValStg?.nodeSettings;
                let value = nodeSettings.process_id;
                log('terminating node', value);
                value && value !== '' && kill(value, 'SIGKILL', function (err) {
                    if (err) {
                        log('killing err', err);
                    }
                });
                nodeSettings.port = 0;
                nodeSettings.process_id = 0;
                nodeSettings.is_active = false;
                nodeSettings.dtupdated = Date.now();
                nodeSettings.dtupdatedEn = new Date(Date.now());
                redisValStg.nodeSettings = nodeSettings;
                await redisClient.set(redisKey, JSON.stringify(redisValStg));
                resolve(true)
            } else {
                log('redisKey', redisKey);
                reject('error, redisKey not found!')
            }

        });
    },
    killAll: async props => {
        const { db } = props;
        return new Promise(async (resolve, reject) => {
            try {
                let refTable = 'battle_nodes';
                await fn.createTables({ sqlClient: db, target: refTable });
                let qq = `  
                    SELECT *
                    FROM ${refTable}
                    Where is_deleted = 0;
                    `; // and is_active = 1
                let data = db.prepare(qq).all();
                if (Array.isArray(data) && data.length !== 0) {
                    for (p of data) {
                        if (p.process_id !== 0) {
                            let isProcessActive = await battleNode.checkProcessID(p.process_id);
                            modeDebug && log('isProcessActive', p.process_id, isProcessActive)
                            if (isProcessActive) {
                                try {
                                    log('killing', p.process_id)
                                    kill(p.process_id, 'SIGKILL', function (err) {
                                        if (err) {
                                            modeDebug && log('killing err', err);
                                            reject('error in terminating')
                                        }
                                        try {
                                            let qq = `  
                                                UPDATE battle_nodes
                                                SET is_active = false, is_deleted = true, dtupdated = CURRENT_TIMESTAMP
                                                where is_deleted = false and process_id = ` + p.process_id;
                                            db.prepare(qq).run();
                                        }
                                        catch (e) {
                                            // console.log('error1a: battleNode kill allnodes', e, qq)
                                            modeDebug && log('error1a: battleNode kill allnodes', qq)
                                            // reject('terminated but db cannot updated!')
                                        }
                                    });
                                }
                                catch (e) {
                                    log('cannont kill process')
                                }
                            } else {
                                try {
                                    let qq = `  
                                    UPDATE battle_nodes
                                    SET is_active = false, is_deleted = true, dtupdated = CURRENT_TIMESTAMP
                                    where is_deleted = false and process_id = ` + p.process_id;
                                    db.prepare(qq).run();
                                }
                                catch (e) {
                                    console.log('error2: battleNode kill allnodes', e)
                                    reject('error2: battleNode kill allnodes!')
                                }
                            }
                        } else {
                            try {
                                let qq = `  
                                    UPDATE battle_nodes
                                    SET is_active = false, is_deleted = true, dtupdated = CURRENT_TIMESTAMP
                                    where is_deleted = false and node_key = '${p.node_key}';`;
                                db.prepare(qq).run();
                            }
                            catch (e) {
                                modeDebug && log('error3: battleNode kill allnodes', qq, e)
                                reject('error3: battleNode kill allnodes!')
                            }
                        }
                    }
                    resolve(true)
                } else {
                    // console.log('no process')
                    resolve(true)
                }
            }
            catch (e) {
                console.log('error3: battleNode kill allnodes', e)
                reject(e)
            }
        });
    },
    killAllX: ({ db, redisClient }) => {
        return new Promise(async (resolve, reject) => {
            try {
                let portList = await battleNode.activeNodePorts();
                log('nodes to terminate. get list: lsof -c node -a -i', portList);

                let allNodes = [];
                let nodes = await redisClient.keys(redixPrefix.node + '*');
                if (Array.isArray(nodes)) {
                    for (n of nodes) {
                        const value = await redisClient.get(n);
                        let nodeData = JSON.parse(value);
                        allNodes.push(nodeData.nodeSettings);
                    }
                }
                Array.isArray(portList) && portList.slice(1).forEach(async function (value) {
                    log('terminating node', value);
                    value && value !== '' && kill(value, 'SIGKILL', function (err) {
                        if (err) {
                            log('killing err', err);
                            // reject('error in terminating')
                            exec('killall node', (err, stdout, stderr) => { //ps aux | grep node
                                if (err) {
                                    log(`err: ${err}`);
                                    return;
                                }
                                log(`stdout: ${stdout}`);
                                resolve(stdout)
                            });
                        }
                    });
                    let rltSmi = allNodes.find(an => an.process_id == value);
                    if (rltSmi) {
                        try {
                            let redisKey = rltSmi.redisKey
                            const redisVal = await redisClient.get(redisKey);
                            if (redisVal) {
                                let redisValStg = JSON.parse(redisVal);
                                let nodeSettings = redisValStg?.nodeSettings;
                                nodeSettings.port = 0;
                                nodeSettings.process_id = 0;
                                nodeSettings.is_active = false;
                                nodeSettings.dtupdated = Date.now();
                                nodeSettings.dtupdatedEn = new Date(Date.now());
                                redisValStg.nodeSettings = nodeSettings;
                                await redisClient.set(redisKey, JSON.stringify(redisValStg));
                                resolve(true)
                            } else {
                                reject('error, redisKey not found!')
                            }
                        }
                        catch (e) {
                            console.log('error: logServerStartStop err', e)
                            reject('error, check logs1')
                        }
                    }
                });
                resolve(true)
            }
            catch (e) {
                log('kill all error', e)
            }
        });
    },
    activeNodePorts: (ms = 300) => {
        return new Promise((resolve, reject) => {
            let arr = []
            try {
                //killall node
                exec('lsof -c node -a -i', (err, stdout, stderr) => { //ps aux | grep node
                    if (err) {
                        // node couldn't execute the command
                        return;
                    }
                    var lines = stdout.toString().split('\n');
                    Array.isArray(lines) && lines.map((l, i) => {
                        let liarr = l.split('    ');
                        let porttStg = Array.isArray(liarr) && liarr[1] && liarr[1].length !== 0 && liarr[1].split(' ');
                        // porttStg && console.log('l', i, porttStg[0])
                        porttStg && arr.push(porttStg[0]);
                    })
                    let portList = [...new Set(arr)];
                    // the *entire* stdout and stderr (buffered)
                    console.log(`stdout: ${stdout}`);
                    // console.log(`stderr: ${stderr}`);
                    resolve(portList)
                });
            } catch (e) {
                reject(e)
            }
        });
    },
    createRecord: ({ redisClient, nodeParams }) => {
        let refTable = 'battle_nodes';
        let refTableTasks = 'battle_node_tasks';
        return new Promise(async (resolve, reject) => {
            try {
                for (n of nodeParams) {
                    await battleNode.createRecordFN({
                        redisClient,
                        ...n, refTable: refTable
                    });

                    // await battleNode.createTasks({
                    //     redisClient,
                    //     ...n, refTable: refTableTasks
                    // });
                }
                resolve(true)
            } catch (e2) {
                log('error: battleNode.createRecord ')
                reject(e2);
            }
        });
    },

    createRecordFN: async (props) => {
        const { redisClient, refTable } = props;
        const nodeData = {
            'battleID': props.battleID,
            'node_type': props.type,
            'node_key': props.key,
            'node_key_tag': props.keyTag,
            'pair': props.pair,
            'process_id': props.process_id,
            'port': props.port,
            'is_active': props.is_active,
            'is_deleted': props.is_deleted,
            'node_tasks': JSON.stringify(props.props),
            'dtcreated': Date.now(),
            'dtcreatedEn': new Date(Date.now()),
        }

        return new Promise(async (resolve, reject) => {
            try {
                let redisKey = redixPrefix.node + props.keyTag;
                let redisVal = {};
                nodeData.redisKey = redisKey;
                const value = await redisClient.get(redisKey);
                if (value && typeof value !== 'object' && value !== undefined) {
                    redisVal = JSON.parse(value);
                    redisVal.nodeSettings = nodeData;
                    await redisClient.set(redisKey, JSON.stringify(redisVal));
                } else {
                    redisVal.nodeSettings = nodeData
                    await redisClient.set(redisKey, JSON.stringify(redisVal));
                }

                let valuStg = []
                const valueX = await redisClient.get(refTable);
                if (valueX && typeof valueX !== 'object' && valueX !== undefined) {
                    let valueArr = JSON.parse(valueX);
                    valuStg = [...valueArr, nodeData];
                } else {
                    valuStg.push(nodeData);
                }
                await redisClient.set(refTable, JSON.stringify(valuStg));
                resolve(true)
            } catch (e2) {
                log('error: battleNode.createRecordFN ')
                reject(e2);
            }
        });
    },
    sqlite_createRecordFN: async (props) => {
        const { sqlClient, refTable } = props;
        const nodeData = {
            'battleID': props.battleID,
            'node_type': props.type,
            'node_key': props.key,
            'node_key_tag': props.keyTag,
            'process_id': props.process_id,
            'port': props.port,
            'is_active': props.is_active,
            'is_deleted': props.is_deleted,
            'node_tasks': JSON.stringify(props.props),
        }
        return new Promise(async (resolve, reject) => {
            try {
                await fnxDb.cud.insertOne({
                    db: sqlClient,
                    table: refTable,
                    payload: nodeData,
                    setid: 'id',
                    shortID: true,
                });
                resolve(true)
            } catch (e2) {
                log('error: battleNode.createRecordFN ')
                reject(e2);
            }
        });
    },

    nodeerrors: async props => {
        const { redisClient, sqlClient, query } = props;
        const dt = Date.now();
        return new Promise(async (resolve, reject) => {
            try {
                let resp = [];
                let keystats = await redisClient.keys(redixPrefix.node + '*');
                if (Array.isArray(keystats)) {
                    for (k of keystats) {
                        let statStg = await redisClient.get(k);
                        let stat = statStg && typeof statStg !== 'object' ? JSON.parse(statStg) : statStg;
                        let tag = k.split(':')[1];
                        let pair = tag.split('_')[0];
                        let interval = tag.split('_')[1];
                        let logs = stat.logs;
                        let errors = stat.errors;
                        resp.push({
                            pair,
                            interval,
                            tag,
                            logs,
                            errors,
                        });
                    }
                }

                // let stats = await redisClient.get(redisKeys.fetchMonitor);
                // if (stats) {
                //     let statsSg = JSON.parse(stats);
                //     for (const pair in statsSg) {
                //         if (statsSg.hasOwnProperty(pair)) {
                //             let key = pair.split('_')[0];
                //             resp.push({
                //                 pair: key,
                //                 interval: pair.split('_')[1],
                //                 tag: pair,
                //                 ...statsSg[pair],
                //             })
                //         }
                //     }
                // }

                let sure = Date.now() - dt
                resolve({ data: resp, sure });
            }
            catch (e) {
                console.log('nodelist error', e)
                reject({ data: false });
            }
        });
    },
    tasklistmonitor: async props => {
        const { redisClient, sqlClient, query } = props;
        const dt = Date.now();
        return new Promise(async (resolve, reject) => {
            try {
                let resp = [];
                let keystats = await redisClient.keys(redixPrefix.monitor + '*');
                if (Array.isArray(keystats)) {
                    for (k of keystats) {
                        let statStg = await redisClient.get(k);
                        let stat = statStg && typeof statStg !== 'object' ? JSON.parse(statStg) : statStg;
                        let tag = k.split(':')[2];
                        let statyp = k.split(':')[1];
                        let key = tag.split('_')[0];
                        let interval = tag.split('_')[1];
                        resp.push({
                            pair: key,
                            interval,
                            tag,
                            statType: statyp,
                            stat,
                        });
                    }
                }

                // let stats = await redisClient.get(redisKeys.fetchMonitor);
                // if (stats) {
                //     let statsSg = JSON.parse(stats);
                //     for (const pair in statsSg) {
                //         if (statsSg.hasOwnProperty(pair)) {
                //             let key = pair.split('_')[0];
                //             resp.push({
                //                 pair: key,
                //                 interval: pair.split('_')[1],
                //                 tag: pair,
                //                 ...statsSg[pair],
                //             })
                //         }
                //     }
                // }

                let sure = Date.now() - dt
                resolve({ data: resp, sure });
            }
            catch (e) {
                console.log('nodelist error', e)
                reject({ data: false });
            }
        });
    },
    tasklist: async props => {
        const { redisClient, sqlClient } = props;
        const dt = Date.now();
        // console.log('xxx');
        return new Promise(async (resolve, reject) => {
            try {
                let tasksArr = []
                let nodesArr = [];
                let nodes = await redisClient.keys(redixPrefix.node + '*');
                if (Array.isArray(nodes)) {
                    for (n of nodes) {
                        const value = await redisClient.get(n);
                        let nodeData = JSON.parse(value);
                        nodesArr.push(nodeData.nodeSettings);
                    };


                    let monitorArr = [];
                    let keystats = await redisClient.keys(redixPrefix.monitor + '*');
                    if (Array.isArray(keystats)) {
                        for (k of keystats) {
                            let statStg = await redisClient.get(k);
                            let stat = statStg && typeof statStg !== 'object' ? JSON.parse(statStg) : statStg;
                            let tag = k.split(':')[2];
                            let statyp = k.split(':')[1];
                            // let key = tag.split('_')[0];
                            // let interval = tag.split('_')[1];
                            monitorArr.push({
                                // pair: key,
                                // interval,
                                tag,
                                statType: statyp,
                                stat,
                            });
                        }
                    }

                    nodesArr.map(n => {
                        let taskstg = typeof n.node_tasks !== 'object' ? JSON.parse(n.node_tasks) : n.node_tasks;
                        Array.isArray(taskstg.tasks) && taskstg.tasks.map(async st => {
                            let tskTag = st.taskDesc === 'klineLoader' ? st.taskTag.toLowerCase() : st.taskTag.toLowerCase().slice(3);
                            let monDataStg = monitorArr.filter(m => m.tag == tskTag);
                            let monData = monDataStg ? monDataStg : []
                            // delete monData.tag;
                            let nextRun = battleNode.calcNextWorkTime({cronPeriod: st.period})
                            st.taskParams = (typeof st.taskParams !== 'object') ? JSON.parse(st.taskParams) :  st.taskParams;
                            st.processMon = monDataStg;
                            st.redisKey = n.redisKey;
                            st.is_active = n.is_active;
                            st.process_id = n.process_id;
                            st.battleID = n.battleID;
                            st.port = n.port;
                            st.pair = n.pair || st.taskTag;
                            st.dtcreatedEn = n.dtcreatedEn;
                            st.id = st.taskID;
                            st.nextRun = nextRun;
                            tasksArr.push(st);
                        })
                    })
                }
                let resx = tasksArr;
                let sure = Date.now() - dt
                resolve({ data: resx, sure });
            }
            catch (e) {
                console.log('battle_node_tasks error', e)
                reject({ data: false });
            }
        });
    },
    task: async props => {
        const { sqlClient, slug } = props;
        const dt = Date.now();
        let taskID = Array.isArray(slug) && slug[1];
        return new Promise(async (resolve, reject) => {
            if (taskID) {
                let res = {}
                try {
                    let qq = `  
                        SELECT *
                        FROM battle_node_tasks
                        Where is_deleted = 0 and taskID = '${taskID}'
                        LIMIT 1;
                        `;
                    let resx = await fnxDb.q.query(sqlClient, qq);
                    let resH = Array.isArray(resx) && resx[0];
                    res = { ...resH };
                    try {
                        let qq2 = `  
                        SELECT taskID, taskTag, status_code, status_desc, task_timer, dtcreated
                        FROM battle_node_task_logs
                        Where taskID = '${taskID}'
                        order by dtcreated desc;
                        `;
                        let resL = await fnxDb.q.query(sqlClient, qq2);
                        res = { ...res, logs: resL };
                    }
                    catch (e2) {
                        log('error in log fetch', e2)
                    }
                    let sure = Date.now() - dt
                    resolve({ data: res, sure });
                }
                catch (e) {
                    console.log('battle_node_tasks error', e)
                    reject({ data: false });
                }
            } else {
                console.log('no taskID')
                reject({ data: false });
            }
        });
    },
    createTasks: async mprops => {
        const { redisClient, refTable, props = {}, pair = '_' } = mprops;
        const { tasks = [] } = props;
        return new Promise(async (resolve, reject) => {
            try {
                for (t of tasks) {
                    await battleNode.createTask({
                        redisClient,
                        table: refTable,
                        payload: t,
                        pair,
                    });
                }
                resolve(true)
            } catch (e2) {
                log('error: battleNode.createTasks ')
                reject(e2);
            }
        });
    },

    createTask: async props => {
        return new Promise(async (resolve, reject) => {
            const redisClient = props.redisClient;
            const refTable = props.table;
            const payload = props.payload;
            try {
                const nodeData = {
                    'battleID': props.payload?.battleID,
                    'pair': props.pair,
                    'nodeKey': props.payload?.nodeKey,
                    'period': props.payload?.period,
                    'periodDesc': props.payload?.periodDesc,
                    'script2Run': props.payload?.script2Run,
                    'script2RunUri': props.payload?.script2RunUri,
                    'batch': props.payload?.batch || '',
                    'nodeInterval': props.payload?.nodeInterval || '',
                    'battleInterval': props.payload?.battleInterval || '',
                    "taskType": props.payload?.taskType || '',
                    'taskID': props.payload?.taskID,
                    'taskTag': props.payload?.taskTag,
                    'taskDesc': props.payload?.taskDesc,
                    'taskParams': props.payload?.taskParams,
                    "wsconn": props.payload?.wsconn,
                    "wsconnuri": props.payload?.wsconnuri,
                    'dtcreated': Date.now(),
                    'dtcreatedEn': new Date(Date.now()),
                }
                // log('create task nodeData', nodeData, refTable, props.pair)
                let valuStg = []
                const value = await redisClient.get(refTable);
                if (value && typeof value !== 'object' && value !== undefined) {
                    let valueArr = JSON.parse(value);
                    valuStg = [...valueArr, nodeData];
                } else {
                    valuStg.push(nodeData);
                }
                await redisClient.set(refTable, JSON.stringify(valuStg));
                resolve(true)
                // await fnxDb.cud.insertOne({
                //     db: props.db,
                //     table: props.table,
                //     payload: nodeData,
                //     setid: 'id',
                //     shortID: true,
                // });
            } catch (e2) {
                log('error: battleNode.createTask ')
                reject(e2);
            }
        });
    },
    sqlite_createTasks: async mprops => {
        const { sqlClient, refTable, props = {} } = mprops;
        const { tasks = [] } = props;
        return new Promise(async (resolve, reject) => {
            try {
                for (t of tasks) {
                    await battleNode.createTask({
                        db: sqlClient,
                        table: refTable,
                        payload: t,
                    });
                }
                resolve(true)
            } catch (e2) {
                log('error: battleNode.createTasks ')
                reject(e2);
            }
        });
    },
    sqlite_createTask: async props => {
        return new Promise(async (resolve, reject) => {
            try {
                const nodeData = {
                    'battleID': props.payload?.battleID,
                    'nodeKey': props.payload?.nodeKey,
                    'period': props.payload?.period,
                    'periodDesc': props.payload?.periodDesc,
                    'script2Run': props.payload?.script2Run,
                    'script2RunUri': props.payload?.script2RunUri,
                    'batch': props.payload?.batch || '',
                    'nodeInterval': props.payload?.nodeInterval || '',
                    'battleInterval': props.payload?.battleInterval || '',
                    "taskType": props.payload?.taskType || '',
                    'taskID': props.payload?.taskID,
                    'taskTag': props.payload?.taskTag,
                    'taskDesc': props.payload?.taskDesc,
                    'taskParams': props.payload?.taskParams,
                    "wsconn": props.payload?.wsconn,
                    "wsconnuri": props.payload?.wsconnuri,
                }
                // log('create task nodeData', nodeData, props)
                await fnxDb.cud.insertOne({
                    db: props.db,
                    table: props.table,
                    payload: nodeData,
                    setid: 'id',
                    shortID: true,
                });
                resolve(true)
            } catch (e2) {
                log('error: battleNode.createTask ')
                reject(e2);
            }
        });
    },
    createKlineData: ({ battleID, pairs, battleInterval, intervals, limit, indicatorsWParams, dataRefreshIntervals }) => {
        let klineNodeParams = [];

        var divider = pairs.length > 100 ? 10 : pairs.length > 50 ? 6 : 3;
        var pairArr = fnxCore.chunkify(pairs, divider, true);
        log('chunkify: ', pairArr, pairArr.length)
        for (p of pairs) {
            var lotAdd = 0;
            var lot = pairArr.map((a1, ix) => a1.includes(p) && ix).filter(f => f !== false)[0];
            lot = lot ? lot : 0;
            var sayInte = 0;
            // console.log('lot', p, lot);
            for (inte of intervals) {
                let nodeKey = fnx.generateKey({ inclTime: false }) // Date.now().toString() +  '-' + (+new Date * Math.random()).toString(36).substring(0,6);//Date.now().toString();//uuidv4();
                
                let Ind2Calc = Array.isArray(indicatorsWParams) && indicatorsWParams.filter(b => b.battleParams.timeFrame === inte)
                if (inte !== battleInterval.toString()) {
                    // lotAdd = pairs.length > 100 ? (divider + (sayInte * 10)) : pairs.length > 50 ? (divider + (sayInte * 10)) : (divider + (sayInte * 10));
                    lotAdd = divider * sayInte + 2;
                };
                sayInte ++;

                let taskParams = {
                    battleID,
                    "nodeID": nodeKey,
                    "keyTag": p.toLowerCase() + '_' + inte,
                    "symbol": p.toLowerCase(),
                    "interval": inte,
                    "battleInterval": battleInterval.toString(),
                    "batch": lot,
                    "batchAdd": lotAdd,
                    limit,
                };
                if (Ind2Calc) {
                    taskParams.indicatorsWParams = Ind2Calc;
                };

                let periodCalc = 1 + lot + lotAdd > 59 ? 59 : (1 + lot + lotAdd);
                let nodeParams = {
                    'battleID': battleID,
                    'type': 'kline',
                    'key': nodeKey,
                    "pair": p.toLowerCase(),
                    'keyTag': p.toLowerCase() + '_' + inte,
                    'process_id': 0,
                    'batch': lot,
                    "batchAdd": lotAdd,
                    "battleInterval": battleInterval.toString(),
                    'port': 0,
                    'is_active': true,
                    'is_deleted': false,
                    'props': {
                        'tasks': [
                            {//fetch candleStick Kline!!!!
                                "taskDesc": "klineLoader",
                                "battleID": battleID,
                                "nodeKey": nodeKey,
                                'taskID': nodeKey + '-1',
                                "taskTag": p + '_' + inte,
                                "script2Run": "node.kline.js",
                                "script2RunUri": "node.kline.js " + nodeKey + '-1',
                                'batch': lot.toString(),
                                "nodeInterval": inte,
                                "battleInterval": battleInterval.toString(),
                                "taskType": inte == battleInterval.toString() ? 'main' : 'support',
                                "wsconn": true,
                                "wsconnuri": p.toLowerCase() + '@kline_' + inte,
                                "period": fn.cronTagCreator(inte, periodCalc),
                                "periodDesc": cronstrue.toString(fn.cronTagCreator(inte, periodCalc)),
                                "taskParams": JSON.stringify(taskParams)
                            },//fetch candleStick Kline!!!!
                            //post acts!
                        ]
                    }
                };
                let tx = nodeParams.props.tasks;
                try {
                    if (dataRefreshIntervals && dataRefreshIntervals[inte] && Array.isArray(dataRefreshIntervals[inte])) {
                        log(inte, dataRefreshIntervals[inte]);
                        dataRefreshIntervals[inte].map((ts, ixx) => {
                            let tsJ = {//fetch candleStick Kline!!!!
                                "taskDesc": "klineLoader-updater",
                                "battleID": battleID,
                                "nodeKey": nodeKey,
                                'taskID': nodeKey + '-' + (ixx + 2),
                                "taskTag": p + '_' + inte,
                                "script2Run": "node.kline.js",
                                "script2RunUri": "node.kline.js " + nodeKey + '-' + (ixx + 2),
                                'batch': lot.toString(),
                                "nodeInterval": inte,
                                "battleInterval": battleInterval.toString(),
                                "taskType": inte == battleInterval.toString() ? 'main' : 'support',
                                "wsconn": true,
                                "wsconnuri": p.toLowerCase() + '@kline_' + inte,
                                "period": ts,
                                "periodDesc": cronstrue.toString(ts),
                                "taskParams": JSON.stringify({
                                    battleID,
                                    "nodeID": nodeKey,
                                    "keyTag": p + '_' + inte,
                                    "symbol": p,
                                    "interval": inte,
                                    limit,
                                })
                            };
                            tx.push(tsJ)
                        });
                        nodeParams.props.tasks = tx;
                    } else {
                        // log('no dataRefreshIntervals[inte]', p, inte)
                    }
                }
                catch (a1x) { }
                // console.log('nodeParams', nodeParams)
                klineNodeParams.push(nodeParams);
            };
        };
        return klineNodeParams;
    },
};
const market = exports.market = {

    market_futuresdaily: async (props) => {
        const { redisClient, dbConnMongo, slug, binanceApi, force = false } = props;
        const dt = Date.now();
        return new Promise(async (resolve, reject) => {
            try {
                let resx = []
                let resS = []
                let redisKey = redixPrefix.dataMarket + 'futuresdaily';
                let valuesStg = await redisClient.get(redisKey);
                if (valuesStg && !force) {
                    let values = JSON.parse(valuesStg);
                    for (const pair in values) {
                        // log('pair, pa', pair, values[pair]);
                        let dTmp = {
                            symbol: pair,
                            ...values[pair],
                        };
                        dTmp.openTimeHRF = new Date(values[pair].openTime);
                        dTmp.closeTimeHRF = new Date(values[pair].closeTime);
                        let deltaT = Date.now() - values[pair].openTime;
                        // log('Date.now() - values[pair].openTime', Date.now() - values[pair].openTime)
                        deltaT < 90000000 && resx.push(dTmp);
                    }
                } else { 
                    let rawData = await fnx.nBinance.futuresDaily({
                        binanceApi,
                    });
                    let resSx = {}

                    let redisDataKey = redixPrefix.dataMarket + "futuresDaily".toLowerCase();
                    for (const pair in rawData) {
                        // log('pair, pa', pair, rawData[pair]);
                        let dTmp = {
                            // symbol: pair,
                            ...rawData[pair],
                        };
                        let delta = (parseFloat(dTmp.lastPrice) - parseFloat(dTmp.lowPrice)) / (parseFloat(dTmp.highPrice) - parseFloat(dTmp.lowPrice)) * 100;
                        dTmp.delta = delta;        
                        dTmp.openTimeHRF = new Date(rawData[pair].openTime);
                        dTmp.closeTimeHRF = new Date(rawData[pair].closeTime);
                        dTmp.dteventISO = new Date(Date.now()).toISOString();
                        dTmp.dtcreatedISO = new Date(Date.now()).toISOString();
                        dTmp.src = '4a1c';
                        let deltaT = Date.now() - rawData[pair].openTime;
                        // deltaT < 90000000 && resS.push(dTmp);

                        if (deltaT < 90000000) {
                            resSx[pair] = { ...dTmp }
                            resx.push(dTmp)
                        }
                        // log('Date.now() - values[pair].openTime', Date.now() - values[pair].openTime)
                        // Removed duplicate push to resx array to prevent duplicates when force=1
                    }
                    await redisClient.set(redisDataKey, JSON.stringify(resSx));

                    try {
                        console.log('fetch from mongodb to redis...')
                        const db = dbConnMongo.db(mongoDbCollections.dbName)
                        const coll = db.collection('gauss.logs.futuresDaily');
                        const tPeriod = 60 * 24 * 3; //3gün.
                        let qq = [
                            { $replaceRoot: { newRoot: "$summary" } },
                            {
                                "$addFields": {
                                    "dtCreated": {
                                        "$toDate": "$dtCreated"
                                    }
                                }
                            },
                            { "$sort": { "dtCreated": -1 } },
                            { "$limit": tPeriod },
                            { "$sort": { "dtCreated": 1 } },
                        ];
                        var rawHistory = coll && await coll.aggregate(qq).toArray();
                        let redisDataLogXKey = redixPrefix.dataMarket + 'futuresDailyLog';
                        await redisClient.set(redisDataLogXKey, JSON.stringify(rawHistory));

                    } catch (eM) {
                        console.log('fetch from mongodb to redis... error', eM)
                    }

                    // resx = rawData;
                }

                let sure = Date.now() - dt
                resolve({ data: resx, sure });
            }
            catch (e) {
                console.log('market futures error', e)
                reject({ data: false });
            }
        });
    },
    
    market_futuresdailylog: async (props) => {
        const { redisClient, sqlMarketClient, slug, binanceApi, force = false } = props;
        const dt = Date.now();
        return new Promise(async (resolve, reject) => {
            try {
                let resx = []
                let resS = []
                let redisKey = redixPrefix.dataMarket + 'marketLog';
                let valuesStg = await redisClient.get(redisKey);
                if (valuesStg) {
                    let values = JSON.parse(valuesStg); 
                    resx = Array.isArray(values) ? values.slice(-100) : values;
                } else { 
                    resx = false
                }
                let sure = Date.now() - dt
                resolve({ data: resx, sure });
            }
            catch (e) {
                console.log('market_futuresdailylog error', e)
                reject({ data: false });
            }
        });
    },
    market_futuresdailyMongolog: async (props) => {
        const { redisClient, force = false } = props;
        const tPeriod = -1 * 24 * 60 * 3;
        const dt = Date.now();
        return new Promise(async (resolve, reject) => {
            try {
                let resx = []
                let resS = []
                let redisKey = redixPrefix.dataMarket + 'futuresDailyLog';
                let valuesStg = await redisClient.get(redisKey);
                if (valuesStg) {
                    let values = JSON.parse(valuesStg); 
                    resx = values; // Array.isArray(values) ? values.slice(tPeriod) : values;
                } else { 
                    resx = false
                }
                let sure = Date.now() - dt
                // console.log('resx', resx)
                if(resx && Array.isArray(resx)) {
                    let lag = Date.now() - new Date([...resx].slice(-1)[0]?.dtCreated).getTime();
                    if (lag < (1000 * 10 * 60)) {
                        resolve({ data: resx, sure });
                    } else {
                        // console.log('DateDiff', lag );
                        let resx2 = []
                        let redisKey2 = redixPrefix.dataMarket + 'marketLog';
                        let valuesStg2 = await redisClient.get(redisKey2);
                        if (valuesStg2) {
                            let values2 = JSON.parse(valuesStg2);
                            resx2 = Array.isArray(values2) ? values2 : values2;
                        } else {
                            resx2 = false
                        }
                        // console.log('resx2', resx2)
                        resolve({ data: resx2, sure });
                    }

                } else {
                    resolve({ data: false, sure });
                }
            }
            catch (e) {
                console.log('market_futuresdailylog error', e)
                reject({ data: false });
            }
        });
    },
    market_futuresdaily_live: async (props) => {
        const { binanceApi } = props;
        const dt = Date.now();
        // console.log('xxx', slug);
        return new Promise(async (resolve, reject) => {
            try {
                let resx = await binance.futuresDaily({
                    binanceApi,
                    cb: true,
                });
                // console.log('resx', resx)
                resolve({ data: resx });
            }
            catch (e) {
                console.log('futures daily error', e)
                reject({ data: false });
            }
        });
    },
    klinesWindcx_data: async ({ redisClient, sqlClient, sqlMarketClient, sqlTradesClient, slug }) => {
        let db = sqlClient;
        let dtX = {
            bop: Date.now(),
            bopHR: new Date(Date.now())
        };
        let dbMarket = sqlMarketClient;
        return new Promise(async (resolve, reject) => {
            //get battle params for indicator list.
            let battleV = {}
            let battle_params = {}

            let battle; //get battle settings...
            try {
                const redisBattleVal = await redisClient.get(redisKeys.battle_init);
                let redisBattleValStg = JSON.parse(redisBattleVal);
                if (redisBattleValStg) {
                    redisBattleValStg.battle_params = JSON.parse(redisBattleValStg.battle_params)
                    battle = redisBattleValStg;
                    battle_params = redisBattleValStg.battle_params;
                } else {
                    // redisBattleValStg null
                    // fnx.log('redisBattleValStg null: ', redisBattleValStg)
                }
            }
            catch (e) {
                fnx.log('redisBattleVal err', e)
            }
            let watch = [];
            let b_pairs = battle_params?.parameters?.pairs;
            // let b_tags = b_pairs.map(p => p + '_' + battle_params.battleInterval.toString());
            if (Array.isArray(b_pairs)) {
                for (p of b_pairs) {
                    let tagg = p.toLowerCase() + '_' + battle_params.parameters.battleInterval.toString();
                    let klineData = await klines.get({redisClient, tag: tagg})
                    let taggData = klineData.slice(-1)[0];
                    // fnx.log('taggData', tagg, taggData)
                    let redisKeyMarketDaily = redixPrefix.dataMarket
                    let marketDailyStgx = await redisClient.get(redisKeyMarketDaily + 'futuresdaily');
                    let marketDailyStg = JSON.parse(marketDailyStgx);
                    let marketDaily = marketDailyStg && typeof marketDailyStg == 'object' && marketDailyStg[p];
                    // log('marketDail', p, marketDaily, !marketDaily && marketDailyStg)
                    let dailyData = marketDaily ? {
                        open24h: marketDaily.openPrice,
                        high24h: marketDaily.highPrice,
                        low24h: marketDaily.lowPrice,
                        last24h: marketDaily.lastPrice,
                        quoteVolume24h: marketDaily.quoteVolume,
                        volume24h: marketDaily.volume,
                        priceChange24h: marketDaily.priceChange,
                        priceChangePercent24h: marketDaily.priceChangePercent,
                        weightedAvgPrice24h: marketDaily.weightedAvgPrice,
                        trades24h: marketDaily.count,
                        openTime24h: marketDaily.openTime,
                        closeTime24h: marketDaily.closeTime,
                        delta: (parseFloat(marketDaily.lastPrice) - parseFloat(marketDaily.lowPrice)) / (parseFloat(marketDaily.highPrice) - parseFloat(marketDaily.lowPrice)) * 100,                                    
                        delta24h: (parseFloat(marketDaily.lastPrice) - parseFloat(marketDaily.lowPrice)) / (parseFloat(marketDaily.highPrice) - parseFloat(marketDaily.lowPrice)) * 100,
                        dtcreatedISO: new Date(Date.now()).toISOString(),
                    } : {}

                    //monitor data!
                    let monitors = []
                    let monitorKeysStg = await redisClient.keys(redixPrefix.monitor + '*');
                    let monitorKeys = monitorKeysStg.filter(x => x.includes(tagg));
                    for (k of monitorKeys) {
                        let mData = await redisClient.get(k);
                        monitors.push({
                            src: k, val: mData
                        });
                    };

                    //indicator
                    let symName = p.toLowerCase();
                    let Indicators = []; 
                    try {
                        let results = [];
                        let keys = await redisClient.keys(redixPrefix.dataIndicators + symName + '_*');
                        await Promise.all(
                            keys.map(async (logkey) => {
                                const value = await redisClient.get(logkey);
                                let nodeData = JSON.parse(value);
                                results = nodeData && nodeData.length !== 0 ? [...results, ...nodeData] : results; //[...results, ...nodeData] 
                            })
                        );
                        Indicators = results;
                    } catch (eI) {
                        fnx.log('redis error', redixPrefix.dataIndicators + symName + '_*', eI);
                        let redisKeyIndicator = redixPrefix.dataIndicators + tagg;
                        let IndicatorStgx = await redisClient.get(redisKeyIndicator);
                        Indicators = JSON.parse(IndicatorStgx); 
                    }

                    //strategy
                    let redisKeyStrategy= redixPrefix.dataStrategies + tagg;
                    let StrategyStgx = await redisClient.get(redisKeyStrategy);
                    let Strategies = JSON.parse(StrategyStgx); 


                    watch.push({
                        pair: p,
                        tag: tagg,
                        ...taggData,
                        ...dailyData,
                        lastUpdates: monitors,
                        indicators: Indicators,
                        strategies: Strategies, 
                    });
                }
            } else {
                //reject('failed, no pairs!!')
                fnx.log('failed, no active battle - no pairs!!')
            }
             
            dtX.battleParams = Date.now() - dtX.bop;
            dtX.battleParamsHR = new Date(Date.now()); 
            dtX.kLineIndicatorData = Date.now() - dtX.bop;
            dtX.kLineIndicatorDataHR = new Date(Date.now());

            let resp = {data : {klines: watch, battle_params,
                // battle_params, klineData, klineIndicatorCalculationData, 
                // klineRulesetsCalculationData, 
                dtX}
            }
            resolve(resp)
        });
    },
    getIndicatorsAndParams: () => {
        return new Promise(async (resolve, reject) => {
            // log('rulesetparams', rulesetparams);
            resolve(rulesetparams);
        });
    },
    setrsgTestData: ({ db }) => {
        const dt = Date.now(); 
        let qq = `
        
        INSERT INTO strategies_rsg (rsgID,rulesetName,direction,cond,ruleSet,is_deleted,dtupdated) VALUES
            ('1717747088310-9rydut','CE_Buy','long',1,'[{"id":"RuleSet1717746916635","indicators":["supertrend"],"criteria":[{"id":"Rule1717746929384","name":"supertrend1","indicator":"supertrend","indicatorRef":"supertrend1","item":"directionTxt","rule":"== \`below\`"},{"id":"Rule1717746961264","name":"supertrend5","indicator":"supertrend","indicatorRef":"supertrend5","item":"directionTxt","rule":"== \`below\`"},{"id":"Rule1717746984611","name":"supertrend15","indicator":"supertrend","indicatorRef":"supertrend15","item":"directionTxt","rule":"== \`below\`"}],"name":"Strends","key":"Strends_key","cond":1}]',0,'2024-06-07 07:58:08'),
            ('1714763115580-s7qw6wA','RSI < 20','long',1,'[{"id":"RuleSet1714762954715","indicators":["rsi"],"criteria":[{"id":"Rule1714762967416","name":"RSI Kriteri","indicator":"rsi","indicatorRef":"rsiRef1","item":"indicatorValue","rule":"<20"},{"id":"Rule1714762987114","name":"RSI Kriteri 2","indicator":"rsi","indicatorRef":"rsiRef2","item":"rsiSMAFast","rule":"<20"}],"name":"Rule1","key":"Rule1_key","cond":1},{"id":"RuleSet1714763049385","indicators":["ema"],"criteria":[{"id":"Rule1714763056909","name":"Ema Kriteri","indicator":"ema","indicatorRef":"emaRef","item":"emaPosition","rule":"== \`up\`"}],"name":"Ema Rule","key":"Ema Rule_key","cond":1}]',0,'2024-05-03 19:05:15'),
            ('1714767377770-81mynzB','rulesetGroupName1','short',1,'[{"id":"RuleSet1714767358193","indicators":["rsi"],"criteria":[{"id":"Rule1714767364421","name":"Kriter 1","indicator":"rsi","indicatorRef":"rsiRef","item":"indicatorValue","rule":">60"}],"name":"rag name 1","key":"rag name 1_key","cond":1}]',0,'2024-05-03 20:16:17'),
            ('1714815539538-bn53eaC','Buy_rsi_ema1m_ema5m','long',0,'[{"id":"RuleSet1714815328746","indicators":["rsi"],"criteria":[{"id":"Rule1714815336121","name":"RSI Kriteri","indicator":"rsi","indicatorRef":"rsiRef","item":"indicatorValue","rule":"<30"},{"id":"Rule1714815358545","name":"RSI Kriteri 2","indicator":"rsi","indicatorRef":"rsiRef","item":"rsiSMAFast","rule":"<20"}],"name":"ruleset1","key":"ruleset1_key","cond":1},{"id":"RuleSet1714815422961","indicators":["ema"],"criteria":[{"id":"Rule1714815435893","name":"ema1m","indicator":"ema","indicatorRef":"emaRef_1m","item":"emaTrend","rule":"== \`up\`"},
                    {"id":"Rule1714815499850","name":"ema5m","indicator":"ema","indicatorRef":"emaRef_5m","item":"emaTrend","rule":"== \`up\`"}],"name":"ruleset2_ema","key":"ruleset2_ema_key","cond":1}]',0,'2024-05-04 09:38:59'),
            ('1714983905489-2asbvoD','RSI 40Long','long',1,'[{"id":"RuleSet1714983855550","indicators":["rsi"],"criteria":[{"id":"Rule1714983860637","name":"Kriter 1","indicator":"rsi","indicatorRef":"rsiRef","item":"indicatorValue","rule":"<40"},{"id":"Rule1714983875090","name":"Kriter 2","indicator":"rsi","indicatorRef":"rsiRef","item":"rsiSMAFast","rule":"<40"}],"name":"RuleRSI","key":"RuleRSI_key","cond":1}]',0,'2024-05-06 08:25:05'),
            ('1717588872406-do9hm8','ichiCheck','long',1,'[{"id":"RuleSet1717588751043","indicators":["ichimoku"],"criteria":[{"id":"Rule1717588756477","name":"ichiVsEma","indicator":"ichimoku","indicatorRef":"ichimokuRef","item":"base","rule":"ref > [emaRef_1m]indicatorValue "}],"name":"ichiEma1mCheck","key":"ichiEma1mCheck_key","cond":1}]',0,'2024-06-05 12:01:12'),
            ('1717765439080-e3sszm','Buy_SuperTrend3CE1','long',1,'[{"id":"RuleSet1717765247815","indicators":["chandelierexit","supertrend"],"criteria":[{"id":"Rule1717765254284","name":"CE Kriter","indicator":"chandelierexit","indicatorRef":"chandelierexit","item":"signal","rule":"== \`buy\`"},{"id":"Rule1717765254284_","name":"CE Age Kriter","indicator":"chandelierexit","indicatorRef":"chandelierexit","item":"signalAge","rule":"< 4"},{"id":"Rule1717765359309","name":"supertrend1","indicator":"supertrend","indicatorRef":"supertrend1","item":"directionTxt","rule":"== \`below\`"},{"id":"Rule1717765377731","name":"supertrend5","indicator":"supertrend","indicatorRef":"supertrend5","item":"directionTxt","rule":"== \`below\`"},{"id":"Rule1717765398784","name":"supertrend15","indicator":"supertrend","indicatorRef":"supertrend15","item":"directionTxt","rule":"== \`below\`"}],"name":"CE","key":"CE_key","cond":1}]',0,'2024-06-07 13:03:59'),
            ('1717765964853-cubql5','Sell_SuperTrend3CE1','short',1,'[{"id":"RuleSet1717765812381","indicators":["chandelierexit","supertrend"],"criteria":[{"id":"Rule1717765846736","name":"CE Kriter","indicator":"chandelierexit","indicatorRef":"chandelierexit","item":"signal","rule":"== \`sell\`"},{"id":"Rule1717765254284_","name":"CE Age Kriter","indicator":"chandelierexit","indicatorRef":"chandelierexit","item":"signalAge","rule":"< 4"},{"id":"Rule1717765887221","name":"supertrend1","indicator":"supertrend","indicatorRef":"supertrend1","item":"directionTxt","rule":"== \`above\`"},{"id":"Rule1717765901378","name":"supertrend5","indicator":"supertrend","indicatorRef":"supertrend5","item":"directionTxt","rule":"== \`above\`"},{"id":"Rule1717765917987","name":"supertrend15","indicator":"supertrend","indicatorRef":"supertrend15","item":"directionTxt","rule":"== \`above\`"}],"name":"CE Kriter","key":"CE Kriter_key","cond":1}]',0,'2024-06-07 13:12:44');

     `;
        return new Promise(async (resolve, reject) => {
            await fn.createTables({
                sqlClient: db, target: 'strategies_rsg_init'
            });
            try {
                let resx = await db.exec(qq);
                let sure = Date.now() - dt
                resolve({ data: resx, sure });
            }
            catch (e) {
                console.log('settest data error', e)
                reject({ data: false });
            }
        });
    },
    getrsgx: ({ db, dbConnMongo, query }) => {
        const dt = Date.now();
        return new Promise(async (resolve, reject) => {
            if (dbConnMongo) {
                const db = dbConnMongo.db(mongoDbCollections.dbName)
                const coll = db.collection(mongoDbCollections.dbCollections.strategies);
                const q = {};
                q.is_deleted = 0; 
                if(query.user) {
                    q.user = `'${'query.user'}'`;
                };
                var qStr = {$match: q};
                var qq = [qStr];
                var strategies = await coll.aggregate(qq).toArray();
                // console.log('query', coll, mongoDbCollections.dbCollections.strategies, qq, strategies);
                let sure = Date.now() - dt
                resolve({ data: strategies, sure });
            } else {
                await fn.createTables({
                    sqlClient: db, target: 'strategies_rsg_init'
                });
                try {
                    let qq = `  
                        SELECT *
                        FROM strategies_rsg
                        Where is_deleted = 0;
                        `;
                    let resx = await fnxDb.q.query(db, qq);
                    let sure = Date.now() - dt
                    resolve({ data: resx, sure });
                }
                catch (e) {
                    console.log('get rsh error', e)
                    reject({ data: false });
                }
            }
        });
    },
    saverulesetsgroup: ({ dbConnMongo, sqlMarketClient, body }) => {
        return new Promise(async (resolve, reject) => {
            if (dbConnMongo) {
                const db = dbConnMongo.db(mongoDbCollections.dbName)
                const coll = db.collection(mongoDbCollections.dbCollections.strategies);
                let rawData = JSON.parse(body);
                let raw = rawData;
                rawData.ruleSet = JSON.stringify(rawData.ruleSet).replaceAll("'", "`");
                rawData.rsgID = fnx.generateKey({inclTime: false});
                rawData.is_deleted = 0;
                rawData.dtupdated = new Date(Date.now()).toISOString();
                try {
                    await coll.insertOne(rawData);
                    fnx.log('rawData saved')
                    resolve(true)
                } catch (err) {
                    console.log('hata', err)
                    resolve({
                        error: true,
                        errorDesc: err,
                    })
                }
            } else {
                await fn.createTables({
                    sqlClient: sqlMarketClient, target: 'strategies_rsg_init'
                });
                try {
                    let rawData = JSON.parse(body);
                    let raw = [rawData];
                    rawData.ruleSet = JSON.stringify(rawData.ruleSet).replaceAll("'", "`")
                    const tableName = `strategies_rsg`;
                    await fnxDb.cud.insertOne({
                        db: sqlMarketClient,
                        payload: rawData,
                        table: tableName,
                        setid: 'rsgID',
                        shortID: true,
                        debug: true,
                    });
                    resolve(true)
                } catch (a) {
                    reject(a)
                }
    
            }
        });
    },
    deleterulesetsgroup: ({ dbConnMongo, sqlMarketClient, body }) => {
        return new Promise(async (resolve, reject) => {
            if (dbConnMongo) {
                const db = dbConnMongo.db(mongoDbCollections.dbName)
                const coll = db.collection(mongoDbCollections.dbCollections.strategies);
                try {
                    let rawData = JSON.parse(body);
                    fnx.log('deleterulesetsgroup -', rawData.rsgID);
                    let q = {};
                    q.rsgID = rawData.rsgID;
                    let u = { $set: { "is_deleted": 1 } };
                    coll.updateOne( q, u);
                    resolve(true);

                } catch (e) {
                    fnx.log('updateOne error', e);
                    reject(e)
                }

            } else {
                try {
                    let rawData = JSON.parse(body);
                    const tableName = `strategies_rsg`;
                    const ssqlclean = `
                    DELETE FROM ${tableName}
                    WHERE rsgID = '${rawData.rsgID}';\n\n
                `;
                    await sqlMarketClient.exec(ssqlclean);
                    resolve(true)
                } catch (a) {
                    reject(a)
                }
            }
        });
    },
    getTradeList: ({ redisClient }) => {
        const dt = Date.now();
        return new Promise(async (resolve, reject) => {
            
            let trades = [];
            let nodes = await redisClient.keys(redixPrefix.dataTrades +'*');
            if (Array.isArray(nodes)) {
                for (n of nodes) {
                    const value = await redisClient.get(n);
                    let nodeData = JSON.parse(value);
                    // trades.push(nodeData);
                    trades = nodeData && nodeData.length !== 0 ? [...trades, ...nodeData] : trades;
                }
            }
            // var tables = db.prepare(qKlineTables).all();
            let sure = Date.now() - dt
            resolve({ data: trades, sure });

        });
    },

    getTradeList4Symbol: ({ redisClient, slug }) => {
        const dt = Date.now();
        return new Promise(async (resolve, reject) => {

            let symbol = Array.isArray(slug) && slug[1];
            symbol = symbol ? symbol.toLowerCase() : false;

            let orders = [];
            let trades = [];
            // symbol = symbol.toLowerCase();
            if (symbol) {
                let qP = redixPrefix.dataTrades + symbol + '*';
                let nodes = await redisClient.keys(qP);
                // fnx.log('trades symbol', symbol, qP, nodes)
                if (Array.isArray(nodes)) {
                    for (n of nodes) {
                        const value = await redisClient.get(n);
                        let nodeData = JSON.parse(value);
                        // trades.push(nodeData);
                        trades = nodeData && nodeData.length !== 0 ? [...trades, ...nodeData] : trades;
                    }
                }

                let qPO = redixPrefix.dataOrders + symbol + '*';
                let nodesO = await redisClient.keys(qPO);
                // fnx.log('orders symbol', symbol, qPO, nodesO)
                if (Array.isArray(nodesO)) {
                    for (n of nodesO) {
                        const value = await redisClient.get(n);
                        let nodeData = JSON.parse(value);
                        // trades.push(nodeData);
                        orders = nodeData && nodeData.length !== 0 ? [...orders, ...nodeData] : orders;
                    }
                }
                trades.map(t => {
                    let tOrder = Array.isArray(orders) && orders.find(o => o.orderID == t.orderID);
                    t.orderInfo = tOrder || undefined
                });
                // fnx.log('orders count', orders.length, orders)
                // var tables = db.prepare(qKlineTables).all();
                let sure = Date.now() - dt
                resolve({ data: trades, sure });

            } else {
                reject({ error: 'no symbol' })
            }
        });
    },

    getTradeList4Chart: ({ redisClient }) => {
        const dt = Date.now();
        return new Promise(async (resolve, reject) => {
            let redisDataLogKey = redixPrefix.dataMarket + 'tradesLog';
            let currData = await redisClient.get(redisDataLogKey);
            currData = currData || [];
            try {
                currData = JSON.parse(currData);
            } catch (eL) {
                console.log('eL', eL);
            }
            let redisKey = 'battle_init';
            let battle_init = await redisClient.get(redisKey);
            battle_init = JSON.parse(battle_init);
            var bopStg = battle_init?.dtCreated;
            var bop = bopStg && new Date(new Date(bopStg).setSeconds(0, 0));
            var eopX = new Date(new Date(Date.now()).setSeconds(0, 0));
            var eop = new Date(Math.max(...currData.map(x => x["dt"])));
            eop = eop || eopX;
            let sure = Date.now() - dt;
            let resp = { sure, bop, eop, data: currData,  }
            resolve({ 
                ...resp,
            });

        });
    },
    getTradeList4Chart_arsiv: ({ redisClient }) => {
        const dt = Date.now();
        return new Promise(async (resolve, reject) => {
            
            let trades = [];
            let nodes = await redisClient.keys(redixPrefix.dataTrades +'*');
            if (Array.isArray(nodes)) {
                for (n of nodes) {
                    const value = await redisClient.get(n);
                    let nodeData = JSON.parse(value);
                    // trades.push(nodeData);
                    trades = nodeData && nodeData.length !== 0 ? [...trades, ...nodeData] : trades;
                }
            }

            let redisKey = 'battle_init';
            let battle_init = await redisClient.get(redisKey);
            battle_init = JSON.parse(battle_init);
            var bopStg = battle_init?.dtCreated;
            var bop = bopStg && new Date(new Date(bopStg).setSeconds(0, 0));
            var eop = new Date(new Date(Date.now()).setSeconds(0, 0));

            let battleInit = battle_init;
            let battle_params = battleInit && JSON.parse(battleInit?.battle_params);
            let { parameters } = battle_params || {};
            let { pairs, candleCounts, intervals, battleInterval, trading } = parameters || {};
            const dMin = 60000;
            const intervalLength = {
                "1m": dMin, "5m": 5 * dMin,
                "15m": 15 * dMin, "30m": 30 * dMin,
                "1h": 60 * dMin, "2h": 2 * 60 * dMin,
                "4h": 4 * 60 * dMin, "6h": 6 * 60 * dMin,
                "12h": 12 * 60 * dMin, "1d": 24 * 60 * dMin
            };
            let barWidth = intervalLength[battleInterval?.toString()] || dMin;
            let sure = Date.now() - dt;
            let tradesNg = trades.map(t => ({
                symbol: t.symbol,
                tradeID: t.tradeID,
                entryBarBOP: t.entryBarBOP,
                entryBarBOP_ISO: new Date(t.entryBarBOP).toISOString(),
                entryBudget: t.entryBudget,
                unRealizedPnl: t.unRealizedPnl,
                realizedPnlB4Comm: t.realizedPnlB4Comm,
                realizedPnl: t.realizedPnl,
                closeTime: t.closeTime,
                closeTime_ISO: t.closeTime && new Date(t.closeTime).toISOString(),
            }));
            var bop2 = new Date(Math.min(...tradesNg.map(x => x["entryBarBOP"])));
            var eop2 = new Date(Math.max(...tradesNg.map(x => x["entryBarBOP"])));
            bop = bop || bop2;
            eop = eop2;

            var diffMs = new Date(eop) - new Date(bop);
            var diffMins = Math.round(diffMs / barWidth);

            let nx = []
            for (let x = 0; x <= diffMins; x++) {
                let nBAR = new Date(new Date(bop).getTime() + x * barWidth);
                let tradesB = tradesNg.filter(t =>
                    (
                        //rule #1
                        (t.entryBarBOP == nBAR.getTime())
                        &&
                        (t.closeTime == undefined || t.closeTime > nBAR.getTime())
                    )
                    || 
                    (
                        //rule #2
                        t.entryBarBOP < nBAR.getTime() && (t.closeTime == undefined || t.closeTime > nBAR.getTime())
                    ) 
                );
                // tradesB.map(t => t.tradeStatusBar = 'open');
                let tradesC = tradesNg.filter(t =>
                    (t.closeTime < nBAR.getTime() )
                );

                let positionBudget = tradesB.reduce((acc, x) => acc + parseFloat(x.entryBudget), 0);
                let unRealizedPnl = tradesB.reduce((acc, x) => acc + parseFloat(x.unRealizedPnl), 0);
                let realizedPnl = tradesC.reduce((acc, x) => acc + parseFloat(x.realizedPnl), 0);
                let openTradesNum = tradesB.length;
                let closedTradesNum = tradesC.length;
                let closedpositionBudget = tradesC.reduce((acc, x) => acc + parseFloat(x.entryBudget), 0);
                
                nx.push({
                    dt: nBAR,
                    // openTrades: tradesB,
                    openTradesNum,
                    positionBudget,
                    unRealizedPnl,
                    // closeTrades: tradesC,
                    closedTradesNum,
                    closedpositionBudget,
                    realizedPnl,
                    netPnl: realizedPnl + unRealizedPnl,
                })
            }

            resolve({ 
                // data: trades, 
                bop2,
                sure, bop, barWidth, eop, diffMins, data: nx, //.slice(-10)
            });

        });
    },
    closeOpenTrades: async ({ redisClient, query, section }) => {
        return new Promise(async (resolve, reject) => {
            if (redisClient) {
                // redisClient.connect();
                try {
                    let closeNote = query && query?.pair && query.pair == 'all' ? 'userClosedMass' : 'userClosedPair';
                    let updateNote = query && query?.pair && query.pair == 'all' ? 'closedMass-user' : 'closedPairs-user'
                    // fnxIdx
                    // resolve(closeNote);
                    let battle_params = {}

                    let battle; //get battle settings...
                    let battleInterval;
                    // console.log('redisClient', cli);
                    try {
                        const redisBattleVal = await redisClient.get(redisKeys.battle_init);
                        let redisBattleValStg = JSON.parse(redisBattleVal);
                        redisBattleValStg.battle_params = JSON.parse(redisBattleValStg.battle_params)
                        battle = redisBattleValStg;
                        battle_params = redisBattleValStg.battle_params;
                        battleInterval = battle_params?.parameters?.battleInterval;
                    }
                    catch (e) {
                        fnx.log('redisBattleVal err', e)
                    }
                    battleInterval = battleInterval ? battleInterval.toString() : '1m';

                    let allTrades = [];
                    let nodes = await redisClient.keys(redixPrefix.dataTrades + '*');
                    if (Array.isArray(nodes)) {
                        for (n of nodes) {
                            const value = await redisClient.get(n);
                            let nodeData = JSON.parse(value);
                            // trades.push(nodeData);
                            allTrades = nodeData && nodeData.length !== 0 ? [...allTrades, ...nodeData] : trades;
                        }
                    }
                    // console.log('allTrades', allTrades);

                    let openTrades = []
                    if (Array.isArray(allTrades) && allTrades.length !== 0) {
                        openTrades = Array.isArray(allTrades) && allTrades.filter(t => t.tradeClosed == false);
                    };
                    // fnx.log('openTrades', openTrades);

                    let pairs = [];

                    if (query.pair) {
                        act = true;
                        if (query.pair == 'all') {
                            if (openTrades && Array.isArray(openTrades) && openTrades.length !== 0) {
                                pairs = [...new Set(openTrades.map(a => a.pair))]
                            };
                        } else {
                            pairs = [query.pair]
                        }
                    }
                    // console.log('pairs', pairs)
                    fnx.log('pairs', pairs);

                    let marketTicker;
                    let redisMarketKey = redixPrefix.dataMarket + 'futuresdaily';
                    const marketTickerStg = await redisClient.get(redisMarketKey);
                    if (marketTickerStg) {
                        marketTicker = JSON.parse(marketTickerStg);
                    }
                    // marketTicker && fnx.log('marketTicker', marketTicker);
                    let Results = []
                    if (marketTicker) {
                        for (p of pairs) {
                            let pU = p.toUpperCase();
                            let markData = marketTicker[pU];
                            let lastCandle = {
                                ...markData,
                                close: markData.lastPrice,
                            }
                            // fnx.log('markData', pU, lastCandle);
                            let ax = await fnxIdx.fnTrades.closeTrade({
                                redisClient,
                                symbol: p,
                                xsmi: p + '_' + battleInterval,
                                lastCandle,
                                closeNote,
                                updateNote,
                            });
                            Results.push(ax);
                        }
                    }
                    resolve(Results);
                } catch (eM) {
                    fnx.log('error close Trades', eM)
                    reject('error X')
                }
            } else {
                reject('no db conn redis')
            }
        });
    },
    cleanMarketData: ({ sqlClient, sqlTradesClient }) => {

        return new Promise(async function (resolve, reject) {
            try {
                let qTxt = `
                SELECT name
                FROM sqlite_schema
                WHERE type ='table' 
                AND name NOT LIKE 'sqlite_%'
                AND name LIKE 'kline_%'
                ;
            `;

                var tables = sqlClient.prepare(qTxt).all();
                var sqx = '';
                Array.isArray(tables) && tables.map(t => {
                    sqx += 'drop table if exists ' + t?.name + '; \n\n';
                });
                // log('tables', tables, sqx);
                await sqlClient.exec(sqx);

                let qTxt2 = `
                SELECT name
                FROM sqlite_schema
                WHERE type ='table' 
                AND name NOT LIKE 'sqlite_%'
                AND name LIKE 'market_%'
                ;
            `;
                var tables2 = sqlClient.prepare(qTxt2).all();
                var sqx2 = '';
                Array.isArray(tables2) && tables2.map(t => {
                    sqx2 += 'drop table if exists ' + t?.name + '; \n\n';
                });
                // log('tables', tables, sqx);
                await sqlClient.exec(sqx2);

                let qTxt21 = `
                SELECT name
                FROM sqlite_schema
                WHERE type ='table' 
                AND name NOT LIKE 'sqlite_%'
                AND name LIKE 'indicators_%'
                ;
            `;
                var tables21 = sqlClient.prepare(qTxt21).all();
                var tables21T = sqlTradesClient.prepare(qTxt21).all();
                var sqx21 = '';
                var sqx21T = '';
                Array.isArray(tables21) && tables21.map(t => {
                    sqx21 += 'drop table if exists ' + t?.name + '; \n\n';
                });
                Array.isArray(tables21T) && tables21T.map(t => {
                    sqx21T += 'drop table if exists ' + t?.name + '; \n\n';
                });
                // log('tables', tables, sqx);
                await sqlClient.exec(sqx21);
                await sqlTradesClient.exec(sqx21T);

                let qTxt212 = `
                SELECT name
                FROM sqlite_schema
                WHERE type ='table' 
                AND name NOT LIKE 'sqlite_%'
                AND name LIKE 'strategies_%'
                ;
            `;
                var tables212 = sqlClient.prepare(qTxt212).all();
                var tables212T = sqlTradesClient.prepare(qTxt212).all();
                var sqx212 = '';
                var sqx212T = '';
                Array.isArray(tables212) && tables212.map(t => {
                    if (t?.name !== 'strategies_rsg') {
                        sqx212 += 'drop table if exists ' + t?.name + '; \n\n';
                    }
                });
                Array.isArray(tables212T) && tables212T.map(t => {
                    if (t?.name !== 'strategies_rsg') {
                        sqx212T += 'drop table if exists ' + t?.name + '; \n\n';
                    }
                });
                // log('tables', tables, sqx);
                await sqlClient.exec(sqx212);
                await sqlTradesClient.exec(sqx212T);

                let tqq = ` drop table if exists t_trades; `;
                await sqlTradesClient.prepare(tqq).run();

                let atqq = ` drop table t_orders; `;
                await sqlTradesClient.prepare(atqq).run();

                resolve(true);
            } catch (e) {
                reject(e)
            }
        })
    },
    indicatorCalculations: async ({ sqlClient, sqlTradesClient, slug }) => {
        let db = sqlClient;
        return new Promise(async (resolve, reject) => {
            //get battle params for indicator list.
            let battle = {}
            let battle_params = {}
            let indicators = []
            let indicatorsU = []
            try {
                let refTable = 'battle_init';
                await battle.initBattleTables({ sqlClient, sqlTradesClient });
                let condition = 'is_active = true and is_deleted = false'
                let records = await fnxDb.cud.select({
                    sqlClient, table: refTable, condition
                });
                battle = records[0];
                battle_params = JSON.parse(battle.battle_params)
                battle_params.parameters.indicatorsWParams.map(i => {
                    indicators.push(i.indicator);
                });
                indicatorsU = [...new Set(indicators)]
            }
            catch (e) {
                fnxCore.logger.info('error', e);
            };
            let sqt = 'select t0.*';
            let sqb = ' from (select symbol, max(x.openTime) ot from indicators_calculated x group by symbol) t0 ';
            indicatorsU.map((r, ix) => {
                if (ix == 0) {
                    sqt += `, t${ix + 1}.openTimeHRF`;
                }
                sqt += `, t${ix + 1}.indicatorValue ${r}, t${ix + 1}.indicatorAdditionalData ${r}More`;

                sqb += ` left join indicators_calculated t${ix + 1} on t${ix + 1}.openTime = t0.ot and t${ix + 1}.symbol = t0.symbol and t${ix + 1}.indicator = '${r}' `
            });
            let fSQL = sqt + sqb;

            resolve({ battle_params, indicators: indicatorsU, fSQL })
        });
    },
};

const klines = exports.klines = {
    get: ({
        redisClient, tag, symbol, interval, inclTicker = false,
        respJson = true, respLimit = false }) => {
        return new Promise(async (resolve, reject) => {
            //calc rediskey.
            try {
                let rediskey;
                if (tag) {
                    rediskey = redixPrefix.dataKline + tag;
                }
                let d2Process;

                const redisKlineValues = await redisClient.get(rediskey);
                let data_klines = JSON.parse(redisKlineValues);
                let data_toProcess = Array.isArray(data_klines) && respLimit ? [...data_klines].slice(-1 * respLimit) : (Array.isArray(data_klines) ? data_klines : false);


                let dRedixTickerKey = redixPrefix.dataKlineTicker + tag.toLowerCase();
                let d2Processstg1 = await redisClient.get(dRedixTickerKey);
                let tickerData = JSON.parse(d2Processstg1);
                let lastTicker = tickerData && fnx.nBinance.tick2json(tickerData.k, true, 'array');
                let lastKline = Array.isArray(data_toProcess) && [...data_toProcess].slice(-1)[0];

                if (lastKline && lastTicker && (lastTicker[0]>lastKline[0])) {
                    if (lastKline[0] == lastTicker[0]) {
                        //remove last and append ticker.
                        // fnx.log('remove last and append ticker.', keyTag, lastKline, lastTicker, )
                        let stg1 = [...data_toProcess].slice(0, data_toProcess.length-1);
                        d2Process = [...stg1, lastTicker];
                    } else {
                        //append ticker.
                        // fnx.log('append ticker.', keyTag, lastKline, lastTicker )
                        d2Process = [...data_toProcess, lastTicker];
                    };
                } else {
                    d2Process = Array.isArray(data_toProcess) ? [...data_toProcess] : [];
                }
                // fnx.log('lasts', lastKline, lastTicker)
                if (respJson) {
                    let rArr = [];
                    Array.isArray(d2Process) && d2Process.map(d => {
                        rArr.push({
                            symbol: tag.split('_')[0].toUpperCase(),
                            openTime: d[0],
                            openTimeHRF: new Date(d[0]),
                            closeTime: d[6],
                            closeTimeHRF: new Date(d[6]),
                            "open": d[1],
                            high: d[2],
                            low: d[3],
                            "close": d[4],
                            volume: d[5],
                            quoteVolume: d[7],
                            trades: d[8],
                            volumeTaker: d[9],
                            quoteVolumeTaker: d[10],
                            "interval": tag.split('_')[1],
                            symi: tag,
                        });
                    });
                    // fnx.log('final', [...rArr].slice(-1))
                    resolve(rArr);
                } else {
                    resolve(d2Process);
                }
            } catch (e) {
                log('error kline get apis - watch gui', e);
                reject('error kline get apis - watch gui');
            }
        });
    },
};
const binance = exports.binance = {
    getTask: taskID => {
        let task = Array.isArray(tasksData) && tasksData.find(t => t.task_id = taskID);
        return task;
    },
    getAllPairs: async (props = {}) => {
        const { pairOnly = true, addPairs = null } = props
        const sBinanceApi = new Binance().options();
        var dt = Date.now();
        return new Promise(async function (resolve, reject) {
            var pairList = sBinanceApi ? await binance.binanceFutureslist(sBinanceApi) : [];
            // console.log('pairList', pairList)
            var data = [...pairList].map(s => {
                return { s: s.symbol, v: fn.numero(s.quoteVolume, 0), vn: parseFloat(s.quoteVolume, 0) }
            }).sort((a, b) => a.vn < b.vn && 1 || -1)
            var pairs = addPairs ? [...addPairs] : [];
            pairOnly && data.map(d => pairs.push(d.s))
            var unique = [...new Set(pairs)];
            var resp = pairOnly ? unique : data
            // console.log('getAllPairs', new Date(Date.now()).toLocaleDateString(), pairOnly ? resp.length : data);
            resolve(resp)
        })

    },
    binanceFutureslist: async (sBinanceApi) => {
        return new Promise(async (resolve) => {
            var liste = []
            var symbolList = await sBinanceApi.futuresDaily();
            for (var key in symbolList) {
                const { symbol, priceChange, priceChangePercent,
                    lastPrice, openPrice, highPrice, lowPrice, volume, quoteVolume,
                    openTime } = symbolList[key]
                // log('Ddelta', Date.now() - openTime);
                const s = {
                    symbol, priceChange, priceChangePercent,
                    lastPrice, openPrice, highPrice, lowPrice, volume, quoteVolume,
                    openTime: new Date(openTime).toISOString()
                };
                (Date.now() - openTime < 87000000) && liste.push(s);
            }
            resolve(liste)
        });
    },

    futuresDaily: async (pr = {}) => {
        const { task, binanceApi, db, cb, } = pr;
        try {
            var dt = Date.now();
            let resp = await fn.futuresDaily({ binanceApi, db, lastN: 5, cb });
            return cb ? resp : true
        } catch (e) {
            console.log('futuresDaily error', e)
            return false
        }
    },
    futuresFundingRate: async (pr = {}) => {
        const { task, binanceApi, db } = pr;
        try {
            var dt = Date.now();
            await fn.futuresFundingRate({
                binanceApi, db, lastN: 5
            });
            return true
        } catch (e) {
            console.log('futuresFundingRate error', e)
            return false
        }
    },
    futuresQuote: async (pr = {}) => {
        const { task, binanceApi, db } = pr;
        try {
            await fn.futuresQuote({ binanceApi, db, lastN: 5 });
            return true
        } catch (e) {
            console.log('futuresQuote error', e)
            return false
        };
    },
    futuresMarkPrice: async (pr = {}) => {
        const { task, binanceApi } = pr;
        try {
            await fn.futuresMarkPrice({ binanceApi });
            return true
        } catch (e) {
            console.log('futuresMarkPrice error', e)
            return false
        }
    },
    futuresPrices: async (pr = {}) => {
        const { task, binanceApi, db } = pr;
        try {
            await fn.futuresPrices({ db, binanceApi, savetoFile: true });
            return true
        } catch (e) {
            console.log('futuresPrices error', e)
            return false
        }
    },
    futuresExchangeInfo: async (pr = {}) => {
        const { task, binanceApi } = pr;
        try {
            await fn.futuresExchangeInfo({ binanceApi });
            return true
        } catch (e) {
            console.log('futuresExchangeInfo error', e)
            return false
        };
    },
};
const fn = {
    numero: (num, dig = 2) => {
        if (num) {
            var resp = parseFloat(num).toLocaleString('tr-TR', {
                minimumFractionDigits: dig,
                maximumFractionDigits: dig
            });
            //console.log('numero', num, resp);
            return resp;
        } else {
            return false;
        }
    },
    cronTagCreator: (interval, seconds = 1) => {
        let timeUnit = interval.slice(-1);
        let timeA = interval.slice(0, -1);
        let cron = ''
        switch (timeUnit) {
            case 'm':
                cron = (seconds ? seconds.toString() + ' ' : '') + '*/' + timeA + ' * * * *';
                break;
            case 'h':
                cron = (seconds ? seconds.toString() + ' ' : '') + '0 */' + timeA + ' * * *';
                break;
            case 'd':
                cron = (seconds ? seconds.toString() + ' ' : '') + '0 0 */' + timeA + ' * *';
                break;
            case 'M':
                cron = (seconds ? seconds.toString() + ' ' : '') + '0 0 * */' + timeA + ' *';
                break;

            default:
                cron = (seconds ? seconds.toString() + ' ' : '') + '3 3 3 3 *';
        }
        return cron;
    },
    createTables: ({ sqlClient, target }) => {
        const q_battle_init = `
    create table IF NOT EXISTS battle_init ( 
        battleID text primary key not null,
        battle_type text NOT NULL DEFAULT(''), 
        battle_params text not null DEFAULT(''), 
        is_active bool not null DEFAULT(true),
        is_deleted bool not null DEFAULT(false),
        dtcreated DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP),
        dtupdated DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP)
        );
`;
        const q_trade_orders = `
    create table IF NOT EXISTS t_orders ( 
        orderID text primary key not null,
        symbol text NOT NULL DEFAULT(''), 
        orderTransactionType text NOT NULL DEFAULT(''), 
        orderTransactionSubType text NOT NULL DEFAULT(''), 
        orderType text NOT NULL DEFAULT(''), 
        orderPrice text not null DEFAULT(''), 
        orderBudget int not null DEFAULT(0),
        orderAmount text not null DEFAULT(''), 
        orderTime int not null DEFAULT(0),
        orderTimeEn text not null DEFAULT(''), 
        orderNote text not null DEFAULT(''), 
        orderSrc_ref text not null DEFAULT(''), 
        orderSrc_successStrategyID text not null DEFAULT(''), 
        orderSrc_successStrategyName text not null DEFAULT(''), 
        orderSrc_candle text not null DEFAULT(''), 
        orderSrc_isNewBar bool not null DEFAULT(true),
        dtcreated DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP),
        dtupdated DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP)
        );
`;

        const q_trade_trades = `
create table IF NOT EXISTS t_trades ( 
    pair text NOT NULL DEFAULT(''), 
    pacalNo integer not null DEFAULT(1),
    orderID text not null DEFAULT(''),
    tradeID text primary key not null,
    tradeNo integer not null DEFAULT(0),
    tradeSubNo integer not null DEFAULT(0),
    tradeLifeTime integer not null DEFAULT(0), 
    tradeClosed bool not null DEFAULT(false),
    direction text NOT NULL DEFAULT(''), 
    entryPrice text not null DEFAULT(''), 
    entryAmount int not null DEFAULT(0),
    entryBudget text not null DEFAULT(''), 
    entryBarBOP int not null DEFAULT(0),
    entryTime int not null DEFAULT(0),
    entryTimeEn text not null DEFAULT(''), 
    entryNote text not null DEFAULT(''), 
    closeBarEOP int not null DEFAULT(0),
    closePrice text not null DEFAULT(''), 
    closeTime integer not null DEFAULT(0),
    closeTimeEn text not null DEFAULT(''), 
    closeType text NOT NULL DEFAULT(''), 
    closeNote text not null DEFAULT(''), 
    realizedPips text not null DEFAULT(''), 
    realizedPipRatio text not null DEFAULT(''), 
    realizedPnl text not null DEFAULT(''), 
    realizedPnlB4Comm text not null DEFAULT(''), 
    unRealizedPnl text not null DEFAULT(''), 
    unRealizedPnlB4Comm text not null DEFAULT(''), 
    commission text not null DEFAULT(''), 
    updateNote text not null DEFAULT(''), 
    dtcreated DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP),
    dtupdated DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP)
    );
`;
        const q_battle_nodes = `
    create table IF NOT EXISTS battle_nodes (
        id text primary key not null,
        battleID text not null DEFAULT(''),
        node_type text not null DEFAULT(''),
        node_key text not null DEFAULT(''),
        node_key_tag text not null DEFAULT(''),
        process_id int not null DEFAULT(0),
        port int not null DEFAULT(0),
        is_active bool not null DEFAULT(false),
        is_deleted bool not null DEFAULT(false),
        node_tasks text not null DEFAULT(''),
        dtcreated DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP),
        dtupdated DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP)
    );
`
        const q_battle_node_tasks = `
    create table IF NOT EXISTS battle_node_tasks ( 
        id text primary key not null,
        battleID text not null DEFAULT(''),
        nodeKey text not null DEFAULT(''),
        taskID text NOT NULL DEFAULT(''), 
        taskDesc text not null DEFAULT(''),
        taskTag text not null DEFAULT(''), 
        taskParams text not null DEFAULT(''),
        script2Run text not null DEFAULT(''),
        script2RunUri text not null DEFAULT(''),
        batch text not null DEFAULT(''),
        nodeInterval text not null DEFAULT(''),
        battleInterval text not null DEFAULT(''),
        taskType text not null DEFAULT(''),
        wsconn bool not null DEFAULT(false),
        wsconnuri text not null DEFAULT(''),
        period text not null DEFAULT(''),
        periodDesc text not null DEFAULT(''),
        is_active bool not null DEFAULT(true),
        is_wip bool not null DEFAULT(false),
        is_deleted bool not null DEFAULT(false),
        dtLastRun DATETIME,
        dtLastWebSocketConn DATETIME,
        dtcreated DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP),
        dtupdated DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP)
        );
`;
        const q_battle_node_task_logs = `
    create table IF NOT EXISTS battle_node_task_logs ( 
        id text primary key not null,
        klines text NOT NULL DEFAULT(''), 
        taskID text NOT NULL, 
        taskTag text not null DEFAULT(''), 
        status_code int not null DEFAULT(0), 
        status_desc text not null DEFAULT(''), 
        dtstarted DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP), 
        dtcompleted DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP), 
        task_timer integer not null DEFAULT(0), 
        dtcreated DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP), 
        dtupdated DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP) 
    );
`;
        const q_strategies_rsg = `
    create table IF NOT EXISTS strategies_rsg ( 
        rsgID text primary key not null,
        rulesetName text NOT NULL DEFAULT(''), 
        direction text not null DEFAULT(''), 
        cond bool not null DEFAULT(true),
        ruleSet TEXT,
        is_deleted bool not null DEFAULT(false),
        dtupdated DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP)
        );
`;
        return new Promise(async (resolve, reject) => {
            try {
                if (target === 'battle_init') {
                    let sql = q_battle_init
                    try {
                        var res = sqlClient.exec(sql);
                        resolve(true)
                    }
                    catch (e) {
                        log(sql);
                        reject(e)
                    }
                } else if (target === 'battle_nodes') {
                    let sql = q_battle_nodes
                    try {
                        var res = sqlClient.exec(sql);
                        resolve(true)
                    }
                    catch (e) {
                        log(sql);
                        reject(e)
                    }
                } else if (target === 'battle_node_tasks') {
                    let sql = q_battle_node_tasks
                    try {
                        var res = sqlClient.exec(sql);
                        resolve(true)
                    }
                    catch (e) {
                        log(sql);
                        reject(e)
                    }
                } else if (target === 'strategies_rsg_init') {
                    let sql = q_strategies_rsg
                    try {
                        var res = sqlClient.exec(sql);
                        resolve(true)
                    }
                    catch (e) {
                        log(sql);
                        reject(e)
                    }
                } else if (target === 'trades_orders') {
                    let sql = q_trade_orders
                    try {
                        var res = sqlTradesClient.exec(sql);
                        resolve(true)
                    }
                    catch (e) {
                        log(sql);
                        reject(e)
                    }
                } else if (target === 'trades_trades') {
                    let sql = q_trade_trades
                    try {
                        var res = sqlTradesClient.exec(sql);
                        resolve(true)
                    }
                    catch (e) {
                        log(sql);
                        reject(e)
                    }
                } else if (target === 'battle_node_task_logs') {
                    let sql = q_battle_node_task_logs
                    try {
                        var res = sqlClient.exec(sql);
                        resolve(true)
                    }
                    catch (e) {
                        log(sql);
                        reject(e)
                    }
                };
                resolve(true)
            } catch (e) {
                log('error: fnDB_createTables ', e)
                reject(e)
            }
        });
    },
    futuresDaily: async ({
        db,
        binanceApi,
        cb = false,
        savetoFile = false,
        save2db = true,
        tableName = 'act_binance_futuresDaily',
        lastN = 5,
    }) => {
        const filePath = path.resolve(__dirname, './bin/binance.futuresDaily.json');
        return new Promise(async (resolve, reject) => {
            try {
                let rawData = await binanceApi.futuresDaily();
                // savetoFile && await fnx.clearOldData({ db, tableName, lastN, })
                // savetoFile && await fnx.savetoFile(rawData, filePath);
                // save2db && await fnx.savetoDBJSONArr({db, rawData, tableName
                //     , time1Field: 'openTime', time2Field: 'closeTime'})
                resolve(cb ? rawData : true)
            } catch (e) {
                reject(e)
            }
        });
    },
    futuresQuote: async ({
        db,
        binanceApi,
        cb = false,
        savetoFile = false,
        save2db = true,
        tableName = 'act_binance_futuresQuote',
        lastN = 5,
    }) => {
        const filePath = path.resolve(__dirname, './bin/binance.futuresQuote.json');
        return new Promise(async (resolve, reject) => {
            try {
                let rawData = await binanceApi.futuresQuote();
                // savetoFile && await fnx.clearOldData({ db, tableName, lastN, })
                // savetoFile && await fnx.savetoFile(rawData, filePath);
                // save2db && await fnx.savetoDBJSONArr({
                //     db, rawData, tableName, time1Field: 'time',
                //     debug: false,
                // })
                resolve(cb ? rawData : true)
            } catch (e) {
                reject(e)
            }
        });
    },
    futuresFundingRate: async ({
        db,
        binanceApi,
        cb = false,
        lastN = 5,
        savetoFile = false,
        save2db = true,
        tableName = 'act_binance_futuresFundingRate',
    }) => {
        const filePath = path.resolve(__dirname, './bin/binance.futuresFundingRate.json');
        return new Promise(async (resolve, reject) => {
            try {
                let rawData = await binanceApi.futuresFundingRate();
                // savetoFile && await fnx.savetoFile(rawData, filePath);
                // save2db && await fnx.savetoDBJSON({db, rawData, tableName, time1Field: 'fundingTime'})
                resolve(cb ? rawData : true)
            } catch (e) {
                reject(e)
            }
        });
    },
    futuresMarkPrice: async ({ cb = false, binanceApi, savetoFile = false }) => {
        const filePath = path.resolve(__dirname, './bin/binance.futuresMarkPrice.json');
        return new Promise(async (resolve, reject) => {
            try {
                let rawData = await binanceApi.futuresMarkPrice();
                // savetoFile && await fnx.savetoFile(rawData, filePath);
                resolve(cb ? rawData : true)
            } catch (e) {
                reject(e)
            }
        });
    },
    futuresExchangeInfo: async ({ cb = false, binanceApi, savetoFile = false }) => {
        const filePath = path.resolve(__dirname, './bin/binance.futuresExchangeInfo.json');
        return new Promise(async (resolve, reject) => {
            try {
                let rawData = await binanceApi.futuresExchangeInfo();
                // savetoFile && await fnx.savetoFile(rawData, filePath);
                resolve(cb ? rawData : true)
            } catch (e) {
                reject(e)
            }
        });
    },
    futuresPrices: async ({
        db,
        binanceApi,
        cb = false,
        savetoFile = false,
        save2db = true,
        tableName = 'act_binance_futuresPrices'
    }) => {
        const filePath = path.resolve(__dirname, './bin/binance.futuresPrices.json');
        return new Promise(async (resolve, reject) => {
            try {
                let rawData = await binanceApi.futuresPrices();
                // savetoFile && await fnx.savetoFile(rawData, filePath);
                // save2db && await fnx.savetoDBJSONArr({db, rawData, tableName})
                resolve(cb ? rawData : true)
            } catch (e) {
                reject(e)
            }
        });
    },
};
const backtest = exports.backtest = {

    //https://stackoverflow.com/questions/34096458/passing-node-flags-args-to-child-process
    //https://stackoverflow.com/questions/22646996/how-do-i-run-a-node-js-script-from-within-another-node-js-script
    runScript: (scriptPath, params, callback, io) => {
        // keep track of whether callback has been invoked to prevent multiple invocations
        var invoked = false;
        var child = childProcess.fork(scriptPath, [params]);
        // listen for errors as they may prevent the exit event from firing

        // io && io.emit('serverBCM', {
        //     mType: 'act',
        //     action: 'setActionInfo',
        //     actionValue: 1,
        //     actionDesc: 'testing....',
        //     mAttachment: null,
        // });
        
        child.on('message', function (message) {
            // console.log('process2 Message from Child process : ' + message);
            io && io.emit('serverBCM', {
                mType: 'info',
                mTit: 'Calculating',
                mDesc: 'calculation task ' + message,
                mAttachment: null,
            });

        });
        
        child.on('exit', function (code) {
            console.log(`child exiting: code: `, code);
            callback(code);
            // do some cleanup
        })
        child.on('error', function (err) {
            console.log(`child exiting: failed!`, err);
            callback(false)
            // do some cleanup
        })

        child.on('childProcess error', function (err) {
            console.log('error', err)
            if (invoked) return;
            invoked = true;
            callback(err);
        });
        // listen for errors as they may prevent the exit event from firing
        process.on('error', function (err) {
            console.log('process.on error', err)
            if (invoked) return;
            invoked = true;
            callback(err);
        });

        // execute the callback once the process has finished running
        child.on('childProcess exit', function (code) {
            console.log('exit', code)
            if (invoked) return;
            invoked = true;
            var err = code === 0 ? null : new Error('exit code ' + code);
            callback(err);
        });

    },

    start: props => {
        const { redisClient, dbConn, binanceApi, body, query, io } = props;
        return new Promise(async (resolve, reject) => {
            let ioServer = await global.io;
            try {
                let params = {};
                // await battle.cleanPrevious({redisClient});
                let battleID = fnx.generateKey({ inclTime: false }); // uuidv4();
                try {
                    params = JSON.parse(body)
                } catch (e1) {
                    reject(e1)
                }
                //create Battle Data
                //create Battle Data
                // await battle.initBattleTables({ sqlClient, sqlTradesClient });
                let backtestData = {
                    'battleID': battleID,
                    'battle_type': params.type,
                    'battle_params': JSON.stringify(params),
                    'is_active': true,
                    'is_deleted': false,
                    'dtCreated': new Date(Date.now()).toISOString(),
                    'dtUpdated': new Date(Date.now()).toISOString(),
                    // ...params.parameters,
                }
                // log('io backtest', io, ioServer );
                io && io.emit('serverBCM', {
                    mType: 'info',
                    mTit: 'Battledata received...',
                    mDesc: 'Parameters used for backtesting',
                    mAttachment: backtestData,
                });

                io && io.emit('serverBCM', {
                    mType: 'act',
                    action: 'setActionStatus',
                    actionValue: 1,
                    actionDesc: 'Started Backtesting...',
                    mAttachment: backtestData,
                });

                await backtest.action({
                    redisClient, dbConn, binanceApi, 
                    backtestData, io,
                });

                io && io.emit('serverBCM', {
                    mType: 'act',
                    action: 'setActionStatus',
                    actionValue: 2,
                    actionDesc: 'Completed Data Fetching...',
                    mAttachment: backtestData,
                });
                  
                resolve({ backtestData })

            } catch (e) {
                log('error: battleStart ')
                reject(e)
            }
        });
    },
    action: props => {

        const {backtestData, binanceApi, redisClient, io} = props;
        return new Promise(async (resolve, reject) => {
            //fetch data! (check curr data status, if not fetch data and log stats etc. else check validity,)
            fnx.log('check if data is fresh')
            let dataFresh = true;
            const freshBacktestDataThreshold = 24 * 60; //1gun..

            io && io.emit('serverBCM', {
                mType: 'info',
                mTit: 'Checking if data is fresh',
                mDesc: 'Checking if data is fresh',
                mAttachment: backtestData,
            });

            let keystats = await redisClient.keys(redixPrefix.monitor + '*');
            let allTimes = [];
            if (Array.isArray(keystats)) {
                for (k of keystats) {
                    let svalStg = await redisClient.get(k);
                    let dr = JSON.parse(svalStg);
                    let pairxsmi = k.split(':').slice(-1);
                    let pair = pairxsmi[0].split('_')[0];
                    let pairinterval = pairxsmi[0].split('_')[1];
                    dr = {
                        ...dr,
                        pair,
                        interval: pairinterval,
                    }
                    // console.log('k', k, pairxsmi, pair, pairinterval, dr)
                    // fnx.log('svalStg', k, );
                    allTimes.push(dr);
                }
            };
            //DONE: check if all pairs have initial data and they are fresh!
            let {battle_params} = backtestData || {};
            battle_params = battle_params && JSON.parse(battle_params);
            let pairs = battle_params?.parameters?.pairs;
            let intervals = battle_params?.parameters?.intervals;
            let battleInterval = battle_params?.parameters?.battleInterval;
            // console.log('intervals', intervals, 'battleInterval', battleInterval, keystats, allTimes);
            let dataFreshStg = []
            await Promise.all(
                pairs.map(p => {
                    intervals.map(i => {
                        let filterTimes = Array.isArray(allTimes) && allTimes.filter(a => a.pair.toLowerCase() == p.toLowerCase() && a.interval == i);
                        
                        let freshBacktestDataStatus = filterTimes && filterTimes.length !== 0 ? {
                            last: new Date(Math.min(...allTimes.map(x => new Date(x["dtupdatedEn"])))).toISOString(),
                            delta: (new Date() - new Date(Math.min(...allTimes.map(x => new Date(x["dtupdatedEn"]))))) / 1000 / 60, //dakika.
                        } : {
                            last: '2001-01-01T21:32:25.560Z',
                            delta: (new Date() - new Date('2001-01-01T21:32:25.560Z')) / 1000 / 60
                        }
                        let isFFResh = freshBacktestDataStatus.delta < freshBacktestDataThreshold;
                        // console.log('check vals', p, i, filterTimes, freshBacktestDataStatus, freshBacktestDataThreshold, isFFResh);
                        dataFreshStg.push(isFFResh)
                    })
                })
            );
            // let freshBacktestDataStatus = allTimes && Array.isArray(allTimes) && allTimes.length !== 0 ? {
            //     last: new Date(Math.min(...allTimes.map(x => new Date(x["dtupdatedEn"])))).toISOString(),
            //     delta: (new Date() - new Date(Math.min(...allTimes.map(x => new Date(x["dtupdatedEn"]))))) / 1000 / 60, //dakika.
            // } : {
            //     last: '2001-01-01T21:32:25.560Z',
            //     delta: (new Date() - new Date('2001-01-01T21:32:25.560Z')) / 1000 / 60
            // } freshBacktestDataStatus, freshBacktestDataThreshold, 
            dataFresh = dataFreshStg.every(e => e == true); // freshBacktestDataStatus.delta < freshBacktestDataThreshold
            fnx.log('action - data freshness status', 'isFresh', dataFresh, dataFreshStg);

            if (!dataFresh) {
                io && io.emit('serverBCM', {
                    mType: 'info',
                    mTit: 'data is not fresh',
                    mDesc: 'data is not fresh, started fetching from binance!',
                    mAttachment: backtestData,
                });
                await backtest.initialdatasetup(props);
            };
            try {
                io && io.emit('serverBCM', {
                    mType: 'info',
                    mTit: 'backtest simulation',
                    mDesc: 'backtest simulation started',
                    mAttachment: backtestData,
                });

                await redisClient.set('backtestData', JSON.stringify(backtestData));

                await backtest.startSimulation({
                    backtestData, binanceApi, redisClient, io, 
                });

                io && io.emit('serverBCM', {
                    mType: 'info',
                    mTit: 'backtest simulation',
                    mDesc: 'Completed Data Fetching...', //backtest simulation completed
                    mAttachment: backtestData,
                });

                resolve(true);

            } catch (e) {
                fnx.log('backtest simulation failed!', e);

                io && io.emit('serverBCM', {
                    mType: 'error',
                    mTit: 'backtest simulation failed',
                    mDesc: 'backtest simulation failed',
                    mAttachment: backtestData,
                });

                resolve(false);
            }
            //start iterate 
        });
    },
    initialdatasetup: props => {
        return new Promise(async (resolve, reject) => {
            const dtBOP = Date.now();
            const {backtestData, binanceApi, redisClient, io} = props;
            const battle_params = JSON.parse(backtestData?.battle_params);
            const { parameters, type, dtCreated } = battle_params;
            const { pairs, candleCounts, intervals, battleInterval, indicatorsWParams, rulesets, trading, limit} = parameters;

            //start iterate 
            var xsmi = [];
            pairs.map(p => {
                intervals.map(b => {
                    xsmi.push(p.toLowerCase() + '_' + b);
                })
            })

            var allPairs = xsmi.length; //pairs.length * indicatorsWParams.length;
            // log('params', battle_params, xsmi, allPairs);
            var divider = allPairs > 300 ? 8 : allPairs > 100 ? 6 : 3;
            var pairArr = fnxCore.chunkify(xsmi, divider, true);
            // log('chunkify: ', pairArr, pairArr.length)
            //check if exist any data for calc.

            let keysKline = await redisClient.keys(redixPrefix.data + '*');
            Array.isArray(keysKline) && keysKline.map(async (k) => {
                redisClient.del(k);
            });

            let keystats = await redisClient.keys(redixPrefix.monitor + '*');
            Array.isArray(keystats) && keystats.map(async (k) => {
                redisClient.del(k);
            });

            //else fetch data...
            let progress = [];
            const uProgress = v => progress.push(v);
            let results = await Promise.all(
                pairArr.map(
                    (pairdata, batch) => backtest.fetchDatas({ 
                        batch, pairs: pairdata, 
                        progressUpdate: uProgress, binanceApi, redisClient, io }))
            );
            log('süre initialdatasetup: ', Date.now() - dtBOP, results)
            log('progress initialdatasetup: ', progress)

            let failed = Array.isArray(progress) && progress.filter(p => {p.status == false});
            if (failed && failed.length !== 0) {

                let failedPairs = failed.map(f => f.pair);
                log('progress failed pairs: ', failedPairs, failedPairs.length);

                io && io.emit('serverBCM', {
                    mType: 'info',
                    mTit: 'fetching errors',
                    mDesc: 'fetching errors, retrying...' + failedPairs.toString(),
                    mAttachment: null,
                });

                await backtest.fetchDatas({ 
                    batch, pairs: failedPairs, 
                    progressUpdate: uProgress, binanceApi, redisClient, io 
                });
            }
            resolve(true);
        });

    },
    fetchDatas: props => {
        return new Promise(async (resolve, reject) => {
            const {progressUpdate, batch, binanceApi, redisClient, io} = props;
            //fetch data! (check curr data status, if not fetch data and log stats etc. else check validity,)
            let dtBOP1 = Date.now();
            let resp = true;
            fnx.log('fetching batch...', batch);

            io && io.emit('serverBCM', {
                mType: 'info',
                mTit: 'fetching',
                mDesc: 'fetching batch' + batch,
                mAttachment: null,
            });

            await fnx.sleep((batch * 3 * 700));
            for (pair of props.pairs ) {
                let fnFR = await backtest.fetchData({
                    pair: pair, batch, progressUpdate, 
                    binanceApi, redisClient, io});
                if (!fnFR) resp = fnFR;
            }
            // await fnx.sleep(400)
            io && io.emit('serverBCM', {
                mType: 'info',
                mTit: 'fetched',
                mDesc: 'fetched batch' + batch,
                mAttachment: null,
            });
            fnx.log('fethed', batch, Date.now() - dtBOP1);
            resolve(resp);
            //start iterate 
        });
    },
    fetchData: props => {
        const {binanceApi, redisClient, pair, progressUpdate, batch} = props;
        return new Promise(async (resolve, reject) => {
            let dtBOP = Date.now()
            //fetch data! (check curr data status, if not fetch data and log stats etc. else check validity,)
            try {
                // fnx.log('fetching...', batch, pair);
                // await fnx.sleep(800);
                const limit = 1500;
                const connTimeOut = 12000;
                let symbol = pair.split('_')[0].toUpperCase();
                let interval = pair.split('_')[1];
                let rawData;
                try {
                    rawData = await fnx.promiseTimeout(binanceApi.futuresCandles(symbol, interval, {
                        limit
                    }), connTimeOut);

                    let redisDataKey = redixPrefix.dataKline + pair;
                    await redisClient.set(redisDataKey, JSON.stringify(rawData));

                    try {
                        let lastKlineValue = [...rawData].slice(-1)[0][0];
                        let sStatsValue = {
                            dtupdatedEn: new Date(Date.now()).toISOString(),
                            dataOpen: lastKlineValue,
                            dataOpenEn: new Date(lastKlineValue).toISOString(),
                            barCount: [...rawData].length,
                            typ: 'kline',
                            ref: 'backtest',
                        };

                        // modeDebug && fnx.log('stat save', sRedisKey, symbol, taskTagLw, JSON.stringify(sStatsValue));
                        await redisClient.set(redixPrefix.monitorKline + pair, JSON.stringify(sStatsValue));
                        await redisClient.set(redixPrefix.monitorKlineInit + pair, JSON.stringify(sStatsValue));
                    }
                    catch (eSt) {
                        fnx.log('stat save error', symbol);
                    }

                    progressUpdate({ pair, status: true, batch, sure: Date.now() - dtBOP });
                    resolve(true);
                }
                catch (e) {
                    fnx.log('error fetchdata', e);
                    progressUpdate({ pair, status: false, batch, sure: Date.now() - dtBOP });
                    resolve(false);
                }

                // fnx.log('fethed...', batch, pair);
            } catch (e) {
                progressUpdate({ pair, status: false, batch, sure: Date.now() - dtBOP });
                fnx.log('fetch failed...', batch, pair, e);
                resolve(false);
            }
            //start iterate 
        });
    },
    startSimulation: props => {
        const {backtestData, binanceApi, redisClient, io} = props;
        let dtBOP = Date.now()
        return new Promise(async (resolve, reject) => {
            io && io.emit('serverBCM', {
                mType: 'info',
                mTit: 'simulating',
                mDesc: 'simulating',
                mAttachment: backtestData,
            });
            let battle_params = typeof backtestData?.battle_params !== 'object' ? JSON.parse(backtestData?.battle_params) : backtestData.battle_params;
            const { parameters, type, dtCreated } = battle_params;
            const { pairs, candleCounts, intervals, battleInterval, indicatorsWParams, rulesets, trading, limit} = parameters;

            //start iterate 
            //TODO: check items 2 itarate vs saved locally...
            var xsmi = [];
            pairs.map(p => {
                intervals.map(b => {
                    xsmi.push(p.toLowerCase() + '_' + b);
                })
            });

            // let keysKline = await redisClient.keys(redixPrefix.dataKline + '*');
            // if ( Array.isArray(keysKline) ) {
            //     for (k of keysKline) {
            //         const redisVal = await redisClient.get(k);
            //         let records = redisVal && typeof redisVal !== 'object' ? JSON.parse(redisVal) : redisVal;
            //         // fnx.log('dt_', k, records.slice(-1)[0], fnx.nBinance.tick2json(records.slice(-1)[0]))
            //         let pair = k.replace(redixPrefix.dataKline, "");
            //         let calcInx = await backtest.iterateKlines({
            //             pair, battleInterval, intervals, indicatorsWParams,
            //             io, redisClient, data: records,
            //         });

            //         try {
            //             await fnxCore.main.save2file(calcInx, 'backtest_' + pair + '.json', true);
            //         } catch (e) {
            //             console.log('error save', e)
            //         }
            //     }
            //     fnx.log('TOPLAM SURE', Date.now() - dtBOP);
            // } else {
            //     reject('check local db ?')
            // }



            // await fnx.sleep(2900);
            // fnx.log('indicatorsWParams', indicatorsWParams);
            io && io.emit('serverBCM', {
                mType: 'info',
                mTit: 'simulating',
                mDesc: 'simulating ended.',
                mAttachment: null,
            });
            resolve(true);
        });
    },
    iterateKlines: (props) => {
        const {pair, data, battleInterval, intervals, indicatorsWParams} = props;
        return new Promise(async (resolve, reject) => {
            var dtBOP = Date.now();
            var currentIndex = 0;
            var tickCalc = [];
            const symbol = pair.split('_')[0];
            const interval = pair.split('_')[1];
            let indicators2Calculate = indicatorsWParams.filter(ip => ip.battleParams.timeFrame == interval); //ip.battleParams
            // fnx.log('battleInterval, intervals, indicatorsWParams');
            fnx.log('symbol', Date.now(), symbol, interval, 'started');

            let refData;
            let calcResults;
            try {
                refData = await fnxIdx.indicators.getKlineData({
                    redisClient: false, symbol, keyTag: pair, klineRef: 'closedBar', rawData: data, 
                    tickerData: false, interval, batch: '', taskID: ''
                });
            } catch (e1) {
                fnx.log('indicators.getKlineData e1b', e1)
            }
            
            // fnx.log('refData', Array.isArray(refData) ? refData.slice(-1) : refData);

            let candleCalculationResults = [];
            await Promise.all(
                Array.isArray(refData) && refData.map(async (cndl, candleIndex) => { //candles
                    var bcast = {};
                    const stgData = [...refData].slice(0, candleIndex + 1); //index > 0 ?  : [...data].slice(0, 1)
                    var i = stgData.length;

                    let indicatorCalculationResults = [];
                    indicatorCalculationResults.push({ 
                        pair,
                        id: indX.id,
                        candleIndex: candleIndex,
                        indicator: 'candle',
                        refName: 'candle',
                        indicatorRefName: 'candle',
                        battleInterval: battleInterval?.toString() || '',
                        candle: cndl 
                    });

                    let errorLogs = [];

                    let results = await Promise.all( //indicators...
                        indicators2Calculate.map(async (indX, indicatorIndex) => {
                            let calcStg = {};
                            let indicatorName = indX.indicator;
                            let indicatorrefName = indX.refName;
                            let indicatorParams = typeof indX.battleParams !== 'object' ? JSON.parse(indX.battleParams) : indX.battleParams;
                            let res = {
                                // id: indX.id,
                                // candleIndex: candleIndex,
                                indicator: indicatorName,
                                refName: indicatorrefName,
                                indicatorRefName: indicatorrefName,
                                // pair,
                                // symbol: symbol,
                                // interval: interval,
                            };
                            if (candleIndex > indicatorParams.length) {
                                try {
                                    calcStg = await fnxIdx.calcIndicator[indicatorName]({ 
                                        keyTag: pair, data: stgData, klineRef: 'backtest', 
                                        params: indicatorParams, symbol, mode: 'backtest' });
    
                                    // (i > 1499) && fnx.log('resss1a calcStg', indicatorName, symbol, interval, calcStg);
                                    res = {
                                        ...res,
                                        ...calcStg,
                                    };
                                    let indicatorValue = res.indicatorValue;
                                    try {
                                        let tmp = JSON.parse(indicatorValue);
                                        res.indicatorValue = JSON.stringify(tmp);
                                    }
                                    catch (e) {
                                        res.indicatorValue = typeof res.indicatorValue === "object" ? JSON.stringify(res.indicatorValue) : res.indicatorValue;
                                    }
                                    res.indicatorAdditionalData = res.indicatorAdditionalData && JSON.stringify(res.indicatorAdditionalData) !== '{}' ? JSON.stringify(res.indicatorAdditionalData) : '';
                                    indicatorCalculationResults.push(res);
                                }
                                catch (e) {
                                    let errData = {
                                        symbol,
                                        indicator: indicatorName,
                                        eDesc: '801-main.calculator - calcIndicator Error.',
                                        error: e,
                                    };
                                    fnx.log('error 12', symbol, currentIndex, e)
                                    errorLogs.push(errData);
                                    res = {
                                        ...res,
                                        error: 'err idx not calc',
                                    };
                                    indicatorCalculationResults.push(res);
                                }
                            }
                            // if (i > 1499) {
                            //     fnx.log('symbol - indicators < candles', symbol, interval, candleIndex, indicatorName, res);
                            // }

                            // currentIndex > 1498 && i > indicatorsWParams?.length && fnx.log('idx', Date.now(), {
                            //     i, symbol: symbol, interval: interval, indicatorInfo: idx, indicatorIndex
                            // });
                            return {candleIndex, indicatorIndex}
                        }),
                    );
                    candleCalculationResults.push(indicatorCalculationResults);
                    // i > 1499 && fnx.log('ic', symbol, i, indicators2Calculate.length, results)
                    // indicatorCalculationResults.push(results);
                    
                    // indicatorSira > 1498 && i > indicatorsWParams?.length && fnx.log('idx', Date.now(), {
                    //     i, symbol: symbol, interval: interval, indicatorSira, iCalc
                    // });
                
                }),
            );
            fnx.log('symbol', Date.now(), symbol, candleCalculationResults.length);
            resolve(candleCalculationResults)
        });
    },
    results: async (props) => {
        return new Promise(async (resolve, reject) => {
            try {

                let rawdata = fs.readFileSync('nodes/bin/backtest.summary.json', { encoding: 'utf8', flag: 'r' });
                let summaryData = JSON.parse(rawdata.toString());
                const { backtestData, results } = summaryData;
                const { battleID, battle_type, battle_params, is_active, is_deleted } = backtestData;
                const battleParams = JSON.parse(battle_params);
                const { parameters } = battleParams;
                const { pairs, candleCounts, intervals, battleInterval,
                    indicatorsWParams, rulesets, trading, } = parameters;
                // let backtestData = summaryData.find(b => b.)
                let files = pairs.map(p => {
                    return 'backtest_' + p.toLowerCase() + '_' + battleInterval.toString()
                });

                let stats = []
                for (f of files) {
                    let tmpFileData = fs.readFileSync('nodes/bin/' + f + '.json', { encoding: 'utf8', flag: 'r' });
                    let fData = JSON.parse(tmpFileData.toString());
                    fData && Array.isArray(fData) && stats.push({
                        id: fnx.generateKey({inclTime: false}),
                        pair: f,
                        ...fData.slice(-1)[0].stats,
                    })
                }
                resolve({
                    stats,
                    battleParams,
                    processTimes: results,
                });
            } catch (e) {
                console.log('error red', e);
                reject(false)
            }
        });
    },
    pairresults: async (props) => {
        const {pair} = props;
        return new Promise(async (resolve, reject) => {
            try {
                let fileName = 'backtest_' + pair + '.json';
                let rawdata = fs.readFileSync('nodes/bin/backtest.summary.json', { encoding: 'utf8', flag: 'r' });
                let summaryData = JSON.parse(rawdata.toString());
                const { backtestData, results } = summaryData;
                const { battleID, battle_type, battle_params, is_active, is_deleted } = backtestData;
                const battleParams = JSON.parse(battle_params);
                const { parameters } = battleParams;
                const { pairs, candleCounts, intervals, battleInterval,
                    indicatorsWParams, rulesets, trading, } = parameters;
                let tmpFileData = fs.readFileSync('nodes/bin/' + fileName, { encoding: 'utf8', flag: 'r' });
                let fData = JSON.parse(tmpFileData.toString());
                resolve({
                    backtest: fData,
                    battleParams: parameters,
                });
            } catch (e) {
                console.log('error red', e);
                reject(false)
            }
        });
    },
};
const dex = exports.dex = {
    fdexInfo: async props => {
        const {dexApi} = props;
        return new Promise(async (resolve, reject) => {
            try {
                let data = await dexApi.futuresExchangeInfo();
                // fnx.log('resp info data', data)
                let minimums = {};
                for (let obj of data.symbols) {
                    let filters = { status: obj.status };
                    for (let filter of obj.filters) {
                        if (filter.filterType == "MIN_NOTIONAL") {
                            filters.minNotional = filter.notional; //filter.minNotional
                        } else if (filter.filterType == "PRICE_FILTER") {
                            filters.minPrice = filter.minPrice;
                            filters.maxPrice = filter.maxPrice;
                            filters.tickSize = filter.tickSize;
                        } else if (filter.filterType == "LOT_SIZE") {
                            filters.stepSize = filter.stepSize;
                            filters.minQty = filter.minQty;
                            filters.maxQty = filter.maxQty;
                        }
                    }
                    //filters.baseAssetPrecision = obj.baseAssetPrecision;
                    //filters.quoteAssetPrecision = obj.quoteAssetPrecision;
                    filters.orderTypes = obj.orderTypes;
                    filters.icebergAllowed = obj.icebergAllowed;
                    minimums[obj.symbol] = filters;
                };
                let resp = {
                    exhInfo: data,
                    minimums,
                };

                try {
                    const fileP = path.resolve(path.dirname(__filename), '../../../../../../src/lib/battle.exchangeinfo.json');
                    await fnx.savetoFile(minimums, fileP, true);
                } catch (eF) {
                    console.log('error in saving file', eF);
                }
                // fnx.log('resp info', resp)
                resolve(resp);
            } catch (e) {
                reject(e)
            }
        });
    },
    faccount: async props => {
        const {dexApi, clientPromise, testDex = false, redisClient} = props;
        let BinanceApi = dexApi;
        return new Promise(async (resolve, reject) => {
            try {
                var arr = [
                    fnfAccount(BinanceApi),
                    fnfOpenOrders(BinanceApi, clientPromise, testDex),
                    fnfPRisk(BinanceApi),
                    fnfAllOrders(BinanceApi, clientPromise, testDex),
                    // fnfBalance(BinanceApi),
                ];
                const reflect = (p) => p.then(
                    (v) => ({ v, status: 'fulfilled' }), (e) => ({ e, status: 'rejected' }),
                );
                var resp = {};
                var stats = {}
                Promise.all(arr.map(reflect))
                    .then(async (results) => {
                        const rAccount = results[0].v;
                        const rOOrders = results[1].v;
                        const rPRisk = results[2].v;
                        const rAOrders = results[3].v;
                        // const rBalance = results[4].v;
                        resp.fAccount = rAccount;
                        // resp.fBalance = rBalance;
                        resp.fOpenOrders = rOOrders;
                        resp.fPRisk = (rPRisk);
                        resp.fAllOrders = rAOrders;

                        if (rAccount?.data) {
                            let dta = {...rAccount?.data};
                            delete dta.positions;
                            let redisIndKey = redixPrefix.dex.data + 'account';
                            await redisClient.set(redisIndKey, JSON.stringify(dta));
                        }
                        if (Array.isArray(rPRisk?.data)) {
                            let xxDat = rPRisk?.data;
                            // xxDat?.map(p => {
                            //     p.dtUpdated = Date.now();
                            //     p.dtUpdatedISO = new Date(Date.now()).toISOString();
                            //     p.updateTimeISO = new Date(p.updateTime).toISOString();
                            // });
                            try { 

                                //delete keys!
                                let keysPozs = await redisClient.keys(redixPrefix.dex.dataPositions + '*');
                                Array.isArray(keysPozs) && keysPozs.map(async (k) => {
                                    redisClient.del(k);
                                });

                                for (const p of xxDat) {
                                    let redisIndKey = redixPrefix.dex.dataPositions + p.symbol.toLowerCase();
                                    // fnx.log('redisIndKey', redisIndKey, p);
                                    await redisClient.set(redisIndKey, JSON.stringify(p));
                                }
                            } catch (eIdx) {
                                fnx.log('1108 / fnGetTaskDone indicatorCalculations fnxIdx.main.start', taskTagLw)
                            }
                        } else {
                            console.log('xczxc', rPRisk);
                        }

                        if (Array.isArray(rOOrders?.data)) {
                            let xxDat = rOOrders?.data;
                            // xxDat?.map(p => {
                            //     p.dtUpdated = Date.now();
                            //     p.dtUpdatedISO = new Date(Date.now()).toISOString();
                            //     p.updateTimeISO = new Date(p.updateTime).toISOString();
                            // });
                            try { 
                                //delete keys!
                                let keysPozs = await redisClient.keys(redixPrefix.dex.dataOpenOrders + '*');
                                Array.isArray(keysPozs) && keysPozs.map(async (k) => {
                                    redisClient.del(k);
                                });

                                for (const p of xxDat) {
                                    let redisIndKey = redixPrefix.dex.dataOpenOrders + p.symbol.toLowerCase() + '_' + p.clientOrderId;
                                    // fnx.log('redisIndKey', redisIndKey, p);
                                    await redisClient.set(redisIndKey, JSON.stringify(p));
                                }
                            } catch (eIdx) {
                                fnx.log('1108 / fnGetTaskDone indicatorCalculations fnxIdx.main.start', taskTagLw)
                            }
                        } else {
                            console.log('xczxc', rPRisk);
                        }


                        // resp.fDaily = rDaily
                        resolve(resp)
                    })
                    .catch((e) => {
                        console.log('getAccountStatus', e)
                        resp.error = true;
                        resolve(resp)
                    });

            } catch (e) {
                reject(e)
            }
        });
    },

    faccount2: async props => {
        var {dexApi, redisClient, slug = [], body, listPositions = false} = props;
        redisClient = redisClient || new Redis({ host: '127.0.0.1', port: 6379, });
        dexApi = dexApi || await fnxDex.dex.getDexApi({ redisClient });
        // fnx.log('faccount2 slug', slug);
        return new Promise(async (resolve, reject) => {
            try {
                var arr = [
                    fnfAccount(dexApi),
                    fnfBalance(dexApi),
                    // fnfPRisk(dexApi),
                ];
                const reflect = (p) => p.then(
                    (v) => ({ v, status: 'fulfilled' }), (e) => ({ e, status: 'rejected' }),
                );
                var resp = {};
                var stats = {}
                Promise.all(arr.map(reflect))
                    .then(async (results) => {
                        var rAccount = results[0].v;
                        var rBalance = results[1].v;
                        // const rPRisk = results[1].v;
                        if (slug[1] !== 'showPositions' && !listPositions) {
                            let datt = rAccount.data;
                            delete datt.positions
                            rAccount = {
                                ...rAccount,
                                data: datt,
                            }
                        }
                        resp.fAccount = rAccount;
                        resp.fBalance = rBalance;
                        // resp.fPRisk = (rPRisk);
                        resolve(resp)
                    })
                    .catch((e) => {
                        console.log('getAccountStatus2', e)
                        resp.error = true;
                        resolve(resp)
                    });

            } catch (e) {
                reject(e)
            }
        });
    },
    fpositionsoo: async props => {
        const {dexApi, clientPromise, testDex = false, redisClient} = props;
        let BinanceApi = dexApi;
        return new Promise(async (resolve, reject) => {
            const fxOO = async () => {
                var fOpenOrders = await BinanceApi.futuresOpenOrders();
                return fOpenOrders;
            };
            const fxAc= async () => {
                var fPRisk = await BinanceApi.futuresPositionRisk();
                fPRisk = Array.isArray(fPRisk) && fPRisk.filter(b => parseFloat(b.positionAmt) !== 0);
                Array.isArray(fPRisk) && fPRisk.map(fp => {
                    fp.dtUpdated = Date.now();
                    fp.dtUpdatedISO = new Date(Date.now()).toISOString();
                    fp.updateTimeISO = new Date(fp.updateTime).toISOString();
        
                    let dir = 'SELL';
                    if ((parseFloat(fp.entryPrice) < parseFloat(fp.markPrice)) && parseFloat(fp.unRealizedProfit) > 0) {
                        dir = 'BUY';
                    } else if ((parseFloat(fp.entryPrice) > parseFloat(fp.markPrice)) && parseFloat(fp.unRealizedProfit) < 0)  {
                        dir = 'BUY';
                    }
                    let dirX = fp.positionAmt < 0 ? 'SELL' : 'BUY';
                    fp.direction = dirX
                    // fp.direction = ((parseFloat(fp.entryPrice) < parseFloat(fp.markPrice)) && parseFloat(fp.unRealizedProfit) > 0) ? 'BUY' : 'SELL';
                });
                return fPRisk;

            };
            try {
                var arr = [
                    fxAc(),
                    fxOO(),
                ];
                const reflect = (p) => p.then(
                    (v) => ({ v, status: 'fulfilled' }), (e) => ({ e, status: 'rejected' }),
                );
                var resp = {};
                var stats = {}
                Promise.all(arr.map(reflect))
                    .then(async (results) => {
                        const rAccount = results[0].v;
                        const rOOrders = results[1].v;
                        resp.positions = rAccount;
                        // fnx.log('rAccount', JSON.stringify(rAccount));
                        // fnx.log('rOOrders', JSON.stringify(rOOrders));
                        resp.openOrders = rOOrders;
                        resolve(resp)
                    })
                    .catch((e) => {
                        console.log('get accOo', e)
                        resp.error = true;
                        resolve(resp)
                    });

            } catch (e) {
                reject(e)
            }
        });
    },
    fbalance: async props => {
        const {dexApi} = props;
        return new Promise(async (resolve, reject) => {
            try {
                let acc = await dexApi.futuresBalance();
                resolve(acc);
            } catch (e) {
                reject(e)
            }
        });
    },
    fnAccount: async (BinanceApi) => {
        return new Promise(async (resolve) => {
            var resp = {};
            var dtb = Date.now()
            var fAccount = await BinanceApi.futuresAccount();
            fAccountAssets = fAccount?.assets;
            fAccountAssets = Array.isArray(fAccountAssets) && fAccountAssets.filter(b => parseFloat(b.walletBalance) !== 0);
            fAccount.assets = fAccountAssets
    
            fAccountPositions = fAccount?.positions;
            if (Array.isArray(fAccountPositions)) {
                fAccountPositions = fAccountPositions.filter(b => parseFloat(b.positionAmt) !== 0);
                fAccount.positions = fAccountPositions;
            } else {
                fAccount.positions = [];
            }
            resp.data = fAccount;
            resp.stats = Date.now() - dtb
            resolve(resp)
        });
    },
    getorders: async props => {
        var { redisClient, dexApi, symbol, slug, force = false, ref, threshold = 30000, section } = props;


        redisClient = redisClient || new Redis({ host: '127.0.0.1', port: 6379, });
        dexApi = dexApi || await fnxDex.dex.getDexApi({ redisClient });
        // console.log('symbol', symbol, dexApi);
        return new Promise(async (resolve, reject) => {
            try {
                if (!symbol) {
                    reject(false);
                } else {
                    let redisLogParam = 'getOrders_' + (symbol || p?.symbol);
                    let lRun = await fnx.redisLog({
                        redisClient, action: 'get',
                        param: redisLogParam,
                    });
                    let keepOn = threshold < (Date.now() - (lRun?.dtUpdated || 0));
                    symbol = symbol ? symbol.toUpperCase() : symbol;

                    var resp = {};
                    var stats = {}
                    let dtBOP = Date.now();
                    stats.bop = dtBOP;
                    let redisKeyA = redixPrefix.dex.data + 'ORDERS:' + symbol + ':';

                    if (keepOn) {

                        var fOrders = await fnxDex.dexBinance.futuresAllOrders(
                            {
                                dexApi, redisClient,
                                refCall: 'getOrders',
                                note: 'symbol:' + symbol + ((', ref:' + ref) || ''),
                            }, symbol);
    
                        stats.orders = new Date(Date.now());
                        stats.ordersD = Date.now() - stats.bop;
                        if (Array.isArray(fOrders)) {
                            await Promise.all(
                                fOrders.map(async (o) => {
                                    let redisKey = redisKeyA + o.orderId + '_' + o.clientOrderId;
                                    redisClient && await redisClient.set(redisKey, JSON.stringify(o));
                                }),
                            );
                            stats.eop = new Date(Date.now());
                            stats.eopD = Date.now() - stats.bop;
    
                            lRun = await fnx.redisLog({
                                redisClient, param: redisLogParam, action: 'set'
                            });
    
                            await fnx.logX({
                                redisClient, param: symbol, value: {
                                    section: section || 'dex:getOrders',
                                    note: 'getOrders - futuresAllOrders: ' + symbol,
                                    symbol,
                                    refValues: {
                                        symbol, stats,
                                    }
                                }
                            });
    
                            resolve(fOrders);
                        } else {
                            fnx.log('getOrders error not isArray', symbol, fOrders,)
                            reject('not array in getorders', symbol)
                        }
                    } else {
                        //get from cache!
                        let trades = [];
                        let nodes = await redisClient.keys(redixPrefix.dex.data + 'ORDERS:' + symbol + '*');
                        if (Array.isArray(nodes)) {
                            await Promise.all(
                                nodes.map(async (n) => {
                                    const value = await redisClient.get(n);
                                    let nodeData = JSON.parse(value);
                                    trades = nodeData && nodeData.length !== 0 ? [...trades, ...nodeData] : trades;
                                })
                            );
                        };
                        resolve(trades);
                    }
                }
            } catch (e) {
                fnx.log('getOrders error ', symbol, e)
                reject(e)
            }
        });
    },
}

async function fnfBalance(BinanceApi) {
    return new Promise(async (resolve) => {
        var resp = {};
        var dtb = Date.now()
        var fBalance = await BinanceApi.futuresBalance();
        fBalance = Array.isArray(fBalance) && fBalance.filter(b => parseFloat(b.balance) !== 0);
        resp.data = fBalance;
        resp.stats = Date.now() - dtb
        resolve(resp)
    })
}
async function fnfAccount(BinanceApi) {
    return new Promise(async (resolve) => {
        var resp = {};
        var dtb = Date.now()
        var fAccount = await BinanceApi.futuresAccount();
        fAccountAssets = fAccount?.assets;
        fAccountAssets = Array.isArray(fAccountAssets) && fAccountAssets.filter(b => parseFloat(b.walletBalance) !== 0);
        fAccount.assets = fAccountAssets

        fAccountPositions = fAccount?.positions;
        if (Array.isArray(fAccountPositions)) {
            fAccountPositions = fAccountPositions.filter(b => parseFloat(b.positionAmt) !== 0);
            // fAccountPositions.map(fp => {
            //     // fp.direction = parseFloat(fp.entryPrice) < parseFloat(fp.breakEvenPrice) ? 'BUY' : 'SELL';
            //     let dir = 'SELL';
            //     if ((parseFloat(fp.entryPrice) < parseFloat(fp.markPrice)) && parseFloat(fp.unRealizedProfit) > 0) {
            //         dir = 'BUY';
            //     } else if ((parseFloat(fp.entryPrice) > parseFloat(fp.markPrice)) && parseFloat(fp.unRealizedProfit) < 0)  {
            //         dir = 'BUY';
            //     }
            //     fp.direction = dir
            // });
            fAccount.positions = fAccountPositions;
        } else {
            fAccount.positions = [];
        }
        resp.data = fAccount;
        resp.stats = Date.now() - dtb
        resolve(resp)
    });
}
async function fnfPRisk(BinanceApi) {
    return new Promise(async (resolve) => {
        var dtb = Date.now()
        var resp = {};
        var fPRisk = await BinanceApi.futuresPositionRisk();
        fPRisk = Array.isArray(fPRisk) && fPRisk.filter(b => parseFloat(b.positionAmt) !== 0);
        Array.isArray(fPRisk) && fPRisk.map(fp => {
            fp.dtUpdated = Date.now();
            fp.dtUpdatedISO = new Date(Date.now()).toISOString();
            fp.updateTimeISO = new Date(fp.updateTime).toISOString();

            let dir = 'SELL';
            if ((parseFloat(fp.entryPrice) < parseFloat(fp.markPrice)) && parseFloat(fp.unRealizedProfit) > 0) {
                dir = 'BUY';
            } else if ((parseFloat(fp.entryPrice) > parseFloat(fp.markPrice)) && parseFloat(fp.unRealizedProfit) < 0)  {
                dir = 'BUY';
            }
            fp.direction = dir

            // fp.direction = ((parseFloat(fp.entryPrice) < parseFloat(fp.markPrice)) && parseFloat(fp.unRealizedProfit) > 0) ? 'BUY' : 'SELL';
        });

        resp.data = fPRisk;
        resp.stats = Date.now() - dtb
        resolve(resp)
    })
}
/*
db.getCollection('gauss.binance.allorders').find({}).sort({dateUpdated: -1})
db.getCollection('gauss.binance.allorders').find({}).count();
*/
async function fnfOpenOrders(BinanceApi, clientPromise, testDex) {
    return new Promise(async (resolve) => {
        var dtb = Date.now()
        var resp = {};
        var fOpenOrders = await BinanceApi.futuresOpenOrders();
        Array.isArray(fOpenOrders) && fOpenOrders.map(fp => {
            fp.dtUpdated = Date.now();
            fp.dtUpdatedISO = new Date(Date.now()).toISOString();
            fp.updateTimeISO = new Date(fp.updateTime).toISOString();
        });

        resp.data = fOpenOrders;
        let fTime = Date.now() - dtb;
        resp.stats = fTime;
        if (clientPromise && Array.isArray(fOpenOrders)) {
            const dbConn = await clientPromise;
            const db = dbConn.db('algoweb')
            const coll = db.collection('gauss.binance.allorders');
            for (const ao of fOpenOrders) {
                let tsID = ao.symbol + '_' + ao.orderId.toString();
                ao.tsID = tsID;
                ao.test = testDex;
                ao.timeISO = new Date(ao.time).toISOString();
                await coll.updateOne({ tsID: tsID }, { $set: { ...ao, dateUpdated: new Date() } }, { upsert: true });
            }
            resp.statSaveOrders = Date.now() - dtb - fTime;
        }
        resolve(resp)
    })
}
async function fnfAllOrders(BinanceApi, clientPromise, testDex) {
    return new Promise(async (resolve) => {
        var dtb = Date.now()
        var resp = {};
        var fOpenOrders = await BinanceApi.futuresAllOrders();
        resp.data = fOpenOrders;
        let fTime = Date.now() - dtb;
        resp.stats = fTime;
        if (clientPromise && Array.isArray(fOpenOrders)) {
            const dbConn = await clientPromise;
            const db = dbConn.db('algoweb')
            const coll = db.collection('gauss.binance.allorders');
            for (const ao of fOpenOrders) {
                let tsID = ao.symbol + '_' + ao.orderId.toString();
                ao.tsID = tsID;
                ao.test = testDex;
                ao.timeISO = new Date(ao.time).toISOString();
                await coll.updateOne({ tsID: tsID }, { $set: { ...ao, dateUpdated: new Date() } }, { upsert: true });
            }
            resp.statSaveOrders = Date.now() - dtb - fTime;
        }
        resolve(resp)
    })
}
const gauss = exports.gauss = {
    getBattleParams: props => {
        const { redisClient, body } = props;
        return new Promise(async (resolve, reject) => {
            try {
                if (redisClient) {
                    const battle_init = await redisClient.get('battle_init'); 
                    if(battle_init) {
                        let biStg = JSON.parse(battle_init);
                        const {battle_params} = biStg || {};
                        // console.log('getting with redis biStg', biStg)
                        // console.log('getting with redis battleParams', battle_params)
                        let bpStg = battle_params && JSON.parse(battle_params);
                        const { parameters = {} } = bpStg;
                        resolve(parameters);
                    } else {
                        fnx.log('no battle_init from redisX, checking form body');
                        if (body) {
                            let form = JSON.parse(body);
                            const { parameters = {} } = form;
                            console.log('parameters', parameters, form)
                            resolve(parameters);
                        } else {
                            resolve(false)
                        }

                    }
                } else {
                    console.log('no redisClient')
                    resolve(false)
                }
            } catch (e) {
                console.log('battle_init init Error', e);
                resolve(false);
            }
        });
    },
}
